import java.util.Map;
import java.util.HashMap;

/**
 * Standalone test for JSON escape functionality
 */
public class JsonEscapeTestStandalone {
    
    /**
     * 转义JSON字符串中的特殊字符
     * @param json 原始JSON字符串
     * @return 转义后的JSON字符串
     */
    public static String escapeJsonSpecialChars(String json) {
        if (json == null || json.isEmpty()) {
            return json;
        }

        StringBuilder sb = new StringBuilder(json.length() * 2);
        boolean inString = false;
        boolean escaped = false;
        
        for (int i = 0; i < json.length(); i++) {
            char c = json.charAt(i);
            
            if (!inString) {
                // Outside of string values, just copy characters
                if (c == '"') {
                    inString = true;
                }
                sb.append(c);
                continue;
            }
            
            // Inside string values
            if (escaped) {
                // Previous character was a backslash, this character is escaped
                sb.append(c);
                escaped = false;
                continue;
            }
            
            switch (c) {
                case '"':
                    // End of string
                    inString = false;
                    sb.append(c);
                    break;
                case '\\':
                    // Start of escape sequence
                    escaped = true;
                    sb.append("\\\\");
                    break;
                case '\n':
                    sb.append("\\n");
                    break;
                case '\r':
                    sb.append("\\r");
                    break;
                case '\t':
                    sb.append("\\t");
                    break;
                case '\b':
                    sb.append("\\b");
                    break;
                case '\f':
                    sb.append("\\f");
                    break;
                default:
                    if (c < 0x20) {
                        // 对于其他控制字符，转换为Unicode转义序列
                        sb.append(String.format("\\u%04x", (int) c));
                    } else {
                        sb.append(c);
                    }
                    break;
            }
        }
        return sb.toString();
    }
    
    public static void main(String[] args) {
        System.out.println("Testing JSON escape functionality...");
        
        // Test the original problematic JSON string
        String problematicJson = "{\"2\":\"<p>daddy555555</p><p>3333</p><p>33333</p>\",\"10007\":\"aaa\\nbbbb\",\"10010\":\"3333\"}";
        
        System.out.println("Original JSON: " + problematicJson);
        
        String escapedJson = escapeJsonSpecialChars(problematicJson);
        System.out.println("Escaped JSON:  " + escapedJson);
        
        // Test various special characters
        String testJson = "{\"field1\":\"line1\\nline2\",\"field2\":\"tab\\there\",\"field3\":\"quote\\\"here\"}";
        System.out.println("\nTest JSON: " + testJson);
        String escapedTestJson = escapeJsonSpecialChars(testJson);
        System.out.println("Escaped:   " + escapedTestJson);
        
        // Test normal JSON
        String normalJson = "{\"name\":\"John\",\"age\":30,\"city\":\"New York\"}";
        System.out.println("\nNormal JSON: " + normalJson);
        String escapedNormalJson = escapeJsonSpecialChars(normalJson);
        System.out.println("Escaped:     " + escapedNormalJson);
        
        // Test null and empty
        System.out.println("\nNull test: " + escapeJsonSpecialChars(null));
        System.out.println("Empty test: '" + escapeJsonSpecialChars("") + "'");
        
        System.out.println("\nTest completed successfully!");
    }
}
