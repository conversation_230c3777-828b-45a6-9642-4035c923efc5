# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
RuoYi-Vue-Plus is a multi-tenant management system built on Spring Boot 3.2 with JDK 17/21 support. It's a comprehensive enterprise framework with distributed architecture support.

## Architecture
- **Backend**: Spring Boot 3.2 + MyBatis-Plus + Sa-Token + Redis + MySQL
- **Frontend**: Vue3 + TypeScript + ElementPlus (separate repository: plus-ui)
- **Multi-tenant**: Built-in tenant management with data isolation
- **Distributed**: Supports Redis clustering, distributed locks, and job scheduling

## Module Structure
```
ruoyi-vue-plus/
├── ruoyi-admin/           # Main application (Spring Boot)
├── ruoyi-common/          # Common utilities and frameworks
│   ├── ruoyi-common-core/     # Core framework
│   ├── ruoyi-common-redis/    # Redis utilities
│   ├── ruoyi-common-mybatis/  # MyBatis extensions
│   └── ... (20+ common modules)
├── ruoyi-extend/          # Extended services
│   ├── ruoyi-monitor-admin/   # Spring Boot Admin
│   └── ruoyi-snailjob-server/ # Distributed job scheduling
└── ruoyi-modules/         # Business modules
    ├── ruoyi-system/          # System management
    ├── ruoyi-servicetrack/    # Service tracking module
    ├── ruoyi-generator/       # Code generator
    ├── ruoyi-demo/            # Demo module
    └── ruoyi-workflow/        # Workflow management
```

## Development Commands

### Build & Run
```bash
# Build entire project
mvn clean install

# Run application
mvn spring-boot:run -pl ruoyi-admin

# Package application
mvn clean package -pl ruoyi-admin

# Run JAR
java -jar ruoyi-admin/target/ruoyi-admin.jar
```

### Database Setup
- **MySQL**: Default database, configured in `ruoyi-admin/src/main/resources/application-dev.yml`
- **Redis**: Required for caching and session management
- **Multi-tenant**: Enabled via `tenant.enable=true` in application.yml

### Configuration Files
- **Main config**: `ruoyi-admin/src/main/resources/application.yml`
- **Dev config**: `ruoyi-admin/src/main/resources/application-dev.yml`
- **Database**: MySQL connection in application-dev.yml
- **Redis**: Redis configuration in application-dev.yml

## Key Technologies
- **Authentication**: Sa-Token with JWT
- **Database**: MyBatis-Plus with dynamic datasource
- **Cache**: Redisson with Redis
- **Security**: XSS protection, data encryption, API encryption
- **Monitoring**: Spring Boot Admin
- **Jobs**: SnailJob distributed scheduling
- **Docs**: SpringDoc OpenAPI (Swagger UI at /swagger-ui/index.html)

## Development Notes
- **Port**: 8080 (configurable)
- **Context path**: /
- **Swagger**: http://localhost:8080/swagger-ui/index.html
- **API docs**: http://localhost:8080/v3/api-docs
- **Tenant isolation**: Enabled by default in service-track module
- **Code generation**: Use ruoyi-generator module for CRUD generation

## Common Tasks
- **Add new table**: Use generator module with templates in `ruoyi-generator/src/main/resources/vm/`
- **Add new module**: Create under ruoyi-modules following existing patterns
- **Add new field**: Update entity, VO, mapper XML, and controller
- **Database changes**: Update mapper XML files and entity classes
- **API permissions**: Use `@SaCheckPermission` annotations

## Testing
- Unit tests use Maven profiles (dev/prod/local)
- Test with: `mvn test -Dspring.profiles.active=dev`
- Integration tests use `@Tag("dev")` annotations

## Deployment
- **Docker**: Use provided Dockerfile in ruoyi-admin/
- **JVM**: Recommend JDK 17+ with ZGC for production
- **Environment**: Use `spring.profiles.active=prod` for production