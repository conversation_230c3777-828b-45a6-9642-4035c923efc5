package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *  user_info_changetext
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("user_info_changelog_text")
public class UserInfoChangeText extends BaseChangeText{
    /**
     * user id
     */
    @TableField(value = "user_id")
    private Integer userId;
}
