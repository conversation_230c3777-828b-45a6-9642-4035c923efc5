package org.dromara.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ContactInfoSelection;
import org.dromara.servicetrack.domain.bo.ContactInfoSelectionBo;
import org.dromara.servicetrack.domain.vo.ContactInfoSelectionVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * 联系人选择字段管理 数据层
 *
 * <AUTHOR>
 */
public interface ContactInfoSelectionMapper extends BaseMapperPlus<ContactInfoSelection, ContactInfoSelectionVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ContactInfoSelection> buildWrapper(ContactInfoSelectionBo bo) {
        LambdaQueryWrapper<ContactInfoSelection> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ContactInfoSelection::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, ContactInfoSelection::getProjectId, bo.getProjectId());
        lqw.eq(bo.getContactId() != null, ContactInfoSelection::getContactId, bo.getContactId());
        lqw.eq(bo.getFieldId() != null, ContactInfoSelection::getFieldId, bo.getFieldId());
        lqw.eq(bo.getChoiceId() != null, ContactInfoSelection::getChoiceId, bo.getChoiceId());
        return lqw;
    }

    /**
     * 根据项目ID和联系人ID查询选择字段列表
     *
     * @param projectId 项目ID
     * @param contactId 联系人ID
     * @return 选择字段列表
     */
    List<ContactInfoSelectionVo> selectByProjectAndContact(@Param("projectId") Integer projectId, @Param("contactId") Integer contactId);

    /**
     * 根据项目ID查询所有联系人选择字段
     *
     * @param projectId 项目ID
     * @return 选择字段列表
     */
    List<ContactInfoSelectionVo> selectByProjectId(@Param("projectId") Integer projectId);

    /**
     * 根据字段ID查询选择字段列表
     *
     * @param projectId 项目ID
     * @param contactId 联系人ID
     * @param fieldId   字段ID
     * @return 选择字段列表
     */
    List<ContactInfoSelectionVo> selectByFieldId(@Param("projectId") Integer projectId, 
                                                @Param("contactId") Integer contactId, 
                                                @Param("fieldId") Integer fieldId);
}
