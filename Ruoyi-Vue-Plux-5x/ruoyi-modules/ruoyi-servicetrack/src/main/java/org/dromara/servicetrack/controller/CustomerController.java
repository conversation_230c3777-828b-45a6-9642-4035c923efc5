package org.dromara.servicetrack.controller;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.vo.ListViewFieldVo;
import org.dromara.common.web.core.BaseController;
import org.dromara.servicetrack.domain.bo.CustomerInfoBo;
import org.dromara.servicetrack.domain.bo.CustomerInfoListBo;
import org.dromara.servicetrack.domain.vo.CustomerInfoListVo;
import org.dromara.servicetrack.domain.vo.CustomerInfoVo;
import org.dromara.servicetrack.service.ICustomerInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/customer")
public class CustomerController extends BaseController {
    private final ICustomerInfoService customerInfoService;
    /**
     * 查询客户列表
     */
    @GetMapping("/list")
    public TableDataInfo<CustomerInfoListVo> list(CustomerInfoListBo bo, PageQuery pageQuery) {
        return customerInfoService.selectPageUserList(bo, pageQuery);
    }
    /**
     * 根据Project Id和Customer Id获得客户字段详情
     */
    @GetMapping("/getInfo")
    public R<CustomerInfoVo> getItemInfo(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "CustomerId不能为空") @RequestParam Integer customerId) {
        return R.ok(customerInfoService.getCustomerInfoDetail(projectId, customerId));
    }
    /**
     * 新增客户信息
     */
    @Log(title = "客户信息管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Integer> add(@Validated @RequestBody CustomerInfoBo bo) {
        if(!customerInfoService.checkCustomerNameUnique(bo)){
            var customerNameFieldName = bo.getSpecialFieldName();
            if( customerNameFieldName == null || customerNameFieldName.isEmpty()){
                customerNameFieldName = "客户名称";
            }
            return R.fail("新增客户'" + bo.getCustomerName() + String.format("'失败，%s已存在",customerNameFieldName));
        }
        return R.ok(customerInfoService.insertByBo(bo));
    }

    /**
     * 修改客户信息
     */
    @Log(title = "客户信息管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Integer> edit(@Validated(EditGroup.class) @RequestBody CustomerInfoBo bo) {
        if(!customerInfoService.checkCustomerNameUnique(bo)){
            var customerNameFieldName = bo.getSpecialFieldName();
            if( customerNameFieldName == null || customerNameFieldName.isEmpty()){
                customerNameFieldName = "客户名称";
            }
            return R.fail("修改客户'" + bo.getCustomerName() + String.format("'失败，%s已存在",customerNameFieldName));
        }
        return R.ok(customerInfoService.updateByBo(bo));
    }

    /**
     * 删除客户信息
     *
     * @param ids 主键串
     */
    @Log(title = "客户信息管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(customerInfoService.deleteWithValidByIds(List.of(ids),  true));
    }

    /**
     * 获取列表CustomerInfo字段信息
     */
    @GetMapping("/getListviewFields")
    public R<ListViewFieldVo> getListviewFields(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                @NotNull(message = "Option不能为空") @RequestParam Integer option){
        return R.ok(customerInfoService.getListviewFields(projectId,option));
    }
}
