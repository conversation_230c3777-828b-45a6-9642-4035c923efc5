package org.dromara.servicetrack.service;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.servicetrack.domain.vo.WorkflowTransitionStateVo;
import org.dromara.servicetrack.domain.vo.ItemTempInfoListVo;
import org.dromara.servicetrack.domain.vo.ItemTempInfoVo;

import java.util.List;

/**
 * Item模板管理 服务层
 *
 * <AUTHOR> fei
 * */
public interface IItemTempInfoService {
    List<ItemTempInfoListVo> getItemTempInfoList(Integer projectId, Integer groupId, Integer needItemType,Integer filterType );
}
