package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *  base change text
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BaseChangeText extends BaseField {
    /**
     * changelog_id
     */
    @TableField(value = "changelog_id")
    private Integer changelogId;

    /**
     * change from
     */
    @TableField(value = "change_from")
    private String changeFrom;

    /**
     * change to
     */
    @TableField(value = "change_to")
    private String changeTo;
}
