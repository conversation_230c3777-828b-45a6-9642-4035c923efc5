package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.servicetrack.domain.ContactInfoChangelogText;

/**
 * 联系人信息变更日志长文本视图对象 contact_info_changelog_text
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ContactInfoChangelogText.class)
public class ContactInfoChangelogTextVo extends BaseChangeTextVo {
    /**
     * 联系人ID
     */
    private Integer contactId;

    /**
     * 联系人名称（扩展字段，用于显示）
     */
    private String contactName;

    /**
     * 变更日志ID
     */
    private Integer changelogId;
}
