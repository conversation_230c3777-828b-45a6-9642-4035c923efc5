package org.dromara.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ProjectSetting;
import org.dromara.servicetrack.domain.UserSetting;
import org.dromara.servicetrack.domain.bo.ProjectSettingBo;
import org.dromara.servicetrack.domain.bo.UserSettingBo;
import org.dromara.servicetrack.domain.vo.UserSettingVo;
import java.util.List;

public interface UserSettingMapper extends BaseMapperPlus<UserSetting, UserSettingVo> {

    default LambdaQueryWrapper<UserSetting> buildWrapper(UserSettingBo bo) {
        LambdaQueryWrapper<UserSetting> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, UserSetting::getProjectId, bo.getProjectId());
        lqw.eq(bo.getUserId() != null, UserSetting::getProjectId, bo.getProjectId());
        lqw.eq(bo.getSettingId() != null, UserSetting::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getSettingName()), UserSetting::getSettingName, bo.getSettingName());
        lqw.eq(bo.getSettingOption() != null, UserSetting::getSettingOption, bo.getSettingOption());
        return lqw;
    }
}
