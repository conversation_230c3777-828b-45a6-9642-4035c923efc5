package org.dromara.servicetrack.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * item_timestamp表的simple vo类
 *
 * <AUTHOR> fei
 */
@Data
public class ItemTimestampSimpleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * seq_no
     */
    private Integer seqNo;

    /**
     * created_by
     */
    private Integer createdBy;

    /**
     * created_by_name
     */
    private String createdByName;

    /**
     * created_time
     */
    private String createdTime;

    /**
     * modified_time
     */
    private String modifiedTime;

    /**
     * content
     */
    private String content;

    public ItemTimestampSimpleVo(ItemTimestampVo itemTimestampVo) {
        this.id = itemTimestampVo.getId();
        this.seqNo = itemTimestampVo.getSeqNo();
        this.createdBy = itemTimestampVo.getCreatedBy();
        this.createdByName = itemTimestampVo.getCreatedByName();
        this.createdTime = itemTimestampVo.getCreatedTime();
        this.modifiedTime = itemTimestampVo.getModifiedTime();
        this.content = itemTimestampVo.getContent();
    }
}
