package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemChangelog;

import java.util.Date;

/**
 * 视图对象 item_changelog
 */
@Data
@EqualsAndHashCode(callSuper =true)
@AutoMapper(target = ItemChangelog.class)
public class ItemChangelogVo extends BaseChangelogVo{
    /**
     * item id
     */
    private Integer itemId;
}
