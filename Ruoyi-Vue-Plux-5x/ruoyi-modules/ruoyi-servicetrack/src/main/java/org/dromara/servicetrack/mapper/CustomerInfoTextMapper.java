package org.dromara.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.CustomerInfoText;
import org.dromara.servicetrack.domain.bo.CustomerInfoTextBo;
import org.dromara.servicetrack.domain.vo.CustomerInfoTextVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;

import java.util.List;

/**
 * 客户文本字段管理 数据层
 *
 * <AUTHOR> fei
 */
public interface CustomerInfoTextMapper extends BaseMapperPlus<CustomerInfoText, CustomerInfoTextVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<CustomerInfoText> buildWrapper(CustomerInfoTextBo bo) {
        LambdaQueryWrapper<CustomerInfoText> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, CustomerInfoText::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, CustomerInfoText::getProjectId, bo.getProjectId());
        lqw.eq(bo.getCustomerId() != null, CustomerInfoText::getCustomerId, bo.getCustomerId());
        lqw.eq(bo.getFieldId() != null, CustomerInfoText::getFieldId, bo.getFieldId());
        lqw.like(StringUtils.isNotBlank(bo.getText()), CustomerInfoText::getText, bo.getText());
        return lqw;
    }

    /**
     * 根据项目ID和客户ID查询文本字段列表
     *
     * @param projectId  项目ID
     * @param customerId 客户ID
     * @return 文本字段列表
     */
    List<CustomerInfoTextVo> selectByProjectAndCustomer(@Param("projectId") Integer projectId, @Param("customerId") Integer customerId);

    /**
     * 根据项目ID查询所有客户文本字段
     *
     * @param projectId 项目ID
     * @return 文本字段列表
     */
    List<CustomerInfoTextVo> selectByProjectId(@Param("projectId") Integer projectId);

    /**
     * 根据字段ID查询文本字段列表
     *
     * @param projectId  项目ID
     * @param customerId 客户ID
     * @param fieldId    字段ID
     * @return 文本字段列表
     */
    List<CustomerInfoTextVo> selectByFieldId(@Param("projectId") Integer projectId, 
                                            @Param("customerId") Integer customerId, 
                                            @Param("fieldId") Integer fieldId);
}
