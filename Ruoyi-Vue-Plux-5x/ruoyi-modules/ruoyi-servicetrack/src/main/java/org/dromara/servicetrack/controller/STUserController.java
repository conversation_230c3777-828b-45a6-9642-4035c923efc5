package org.dromara.servicetrack.controller;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.vo.ListViewFieldVo;
import org.dromara.common.web.core.BaseController;
import org.dromara.servicetrack.domain.bo.UserInfoBo;
import org.dromara.servicetrack.domain.bo.UserInfoListBo;
import org.dromara.servicetrack.domain.vo.STUserInfoVo;
import org.dromara.servicetrack.domain.vo.UserInfoListVo;
import org.dromara.servicetrack.service.IUserInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * service track 用户管理
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/user")
public class STUserController extends BaseController {
    private final IUserInfoService userInfoService;

    /**
     * 查询用户列表
     */
    @GetMapping("/list")
    public TableDataInfo<UserInfoListVo> list(UserInfoListBo bo, PageQuery pageQuery) {
        return userInfoService.selectPageUserList(bo, pageQuery);
    }
    /**
     * 根据Project Id和User Id获得用户字段详情
     */
    @GetMapping("/getInfo")
    public R<STUserInfoVo> getItemInfo(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "UserId不能为空") @RequestParam Integer userId) {
        return R.ok(userInfoService.getUserInfoDetail(projectId, userId));
    }
    /**
     * 新增用户信息
     */
    @Log(title = "用户信息管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Integer> add(@Validated @RequestBody UserInfoBo bo) {
        if(!userInfoService.checkUserNameUnique(bo)){
            var userNameFieldName = bo.getSpecialFieldName();
            if( userNameFieldName == null || userNameFieldName.isEmpty()){
                userNameFieldName = "用户名";
            }
            return R.fail("新增用户'" + bo.getUserName() +  String.format("'失败，%s已存在",userNameFieldName));
        }
        else if ( !userInfoService.checkUserPhoneUnique(bo)) {
            return R.fail("新增用户'" + bo.getUserName() + "'失败，手机号码已存在");
        } else if ( !userInfoService.checkUserEmailUnique(bo)) {
            return R.fail("新增用户'" + bo.getUserName() + "'失败，邮箱账号已存在");
        }
        return R.ok(userInfoService.insertByBo(bo));
    }

    /**
     * 修改用户信息
     */
    @Log(title = "用户信息管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Integer> edit(@Validated(EditGroup.class) @RequestBody UserInfoBo bo) {
        if(!userInfoService.checkUserNameUnique(bo)){
            var userNameFieldName = bo.getSpecialFieldName();
            if( userNameFieldName == null || userNameFieldName.isEmpty()){
                userNameFieldName = "用户名";
            }
            return R.fail("修改用户'" + bo.getUserName() +  String.format("'失败，%s已存在",userNameFieldName));
        }
        else if ( !userInfoService.checkUserPhoneUnique(bo)) {
             return R.fail("修改用户'" + bo.getUserName() + "'失败，手机号码已存在");
        } else if ( !userInfoService.checkUserEmailUnique(bo)) {
            return R.fail("修改用户'" + bo.getUserName() + "'失败，邮箱账号已存在");
        }
        return R.ok(userInfoService.updateByBo(bo));
    }

    /**
     * 删除用户信息
     *
     * @param ids 主键串
     */
    @Log(title = "用户信息管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids,@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId) {
        return toAjax(userInfoService.deleteWithValidByIds(List.of(ids), projectId, true));
    }

    /**
     * 获取列表UserInfo字段信息
     */
    @GetMapping("/getListviewFields")
    public R<ListViewFieldVo> getListviewFields(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                @NotNull(message = "Option不能为空") @RequestParam Integer option){
        return R.ok(userInfoService.getListviewFields(projectId,option));
    }
}
