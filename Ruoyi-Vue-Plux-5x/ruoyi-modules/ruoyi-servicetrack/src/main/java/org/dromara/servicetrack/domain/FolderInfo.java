package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.Date;

/**
 * 文件夹信息对象 folder_info
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("folder_info")
public class FolderInfo extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * 项目ID
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * 文件夹ID
     */
    @TableField(value = "folder_id")
    private Integer folderId;

    /**
     * 文件夹名称
     */
    @TableField(value = "folder_name")
    private String folderName;

    /**
     * 文件夹类型
     */
    @TableField(value = "folder_type")
    private Integer folderType;

    /**
     * 文件夹描述
     */
    @TableField(value = "folder_description")
    private String folderDescription;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * 创建者
     */
    @TableField(value = "created_by")
    private Integer createdBy;
}
