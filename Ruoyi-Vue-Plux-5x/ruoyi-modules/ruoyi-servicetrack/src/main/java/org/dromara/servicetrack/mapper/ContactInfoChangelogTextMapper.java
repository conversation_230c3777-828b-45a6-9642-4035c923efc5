package org.dromara.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ContactInfoChangelogText;
import org.dromara.servicetrack.domain.bo.ContactInfoChangelogTextBo;
import org.dromara.servicetrack.domain.vo.ContactInfoChangelogTextVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;

import java.util.List;

/**
 * 联系人信息变更日志长文本管理 数据层
 *
 * <AUTHOR> <PERSON>
 */
public interface ContactInfoChangelogTextMapper extends BaseMapperPlus<ContactInfoChangelogText, ContactInfoChangelogTextVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ContactInfoChangelogText> buildWrapper(ContactInfoChangelogTextBo bo) {
        LambdaQueryWrapper<ContactInfoChangelogText> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ContactInfoChangelogText::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, ContactInfoChangelogText::getProjectId, bo.getProjectId());
        lqw.eq(bo.getChangelogId() != null, ContactInfoChangelogText::getChangelogId, bo.getChangelogId());
        lqw.eq(bo.getContactId() != null, ContactInfoChangelogText::getContactId, bo.getContactId());
        lqw.eq(bo.getFieldId() != null, ContactInfoChangelogText::getFieldId, bo.getFieldId());
        lqw.like(StringUtils.isNotBlank(bo.getChangeFrom()), ContactInfoChangelogText::getChangeFrom, bo.getChangeFrom());
        lqw.like(StringUtils.isNotBlank(bo.getChangeTo()), ContactInfoChangelogText::getChangeTo, bo.getChangeTo());
        return lqw;
    }

    /**
     * 根据项目ID和联系人ID查询变更日志文本列表
     *
     * @param projectId 项目ID
     * @param contactId 联系人ID
     * @return 变更日志文本列表
     */
    List<ContactInfoChangelogTextVo> selectByProjectAndContact(@Param("projectId") Integer projectId, @Param("contactId") Integer contactId);

    /**
     * 根据变更日志ID查询文本变更详情
     *
     * @param projectId   项目ID
     * @param changelogId 变更日志ID
     * @param contactId   联系人ID
     * @return 文本变更详情列表
     */
    List<ContactInfoChangelogTextVo> selectByChangelogId(@Param("projectId") Integer projectId, 
                                                        @Param("changelogId") Integer changelogId, 
                                                        @Param("contactId") Integer contactId);

    /**
     * 根据字段ID查询变更历史
     *
     * @param projectId 项目ID
     * @param contactId 联系人ID
     * @param fieldId   字段ID
     * @return 文本变更历史列表
     */
    List<ContactInfoChangelogTextVo> selectByFieldId(@Param("projectId") Integer projectId, 
                                                    @Param("contactId") Integer contactId, 
                                                    @Param("fieldId") Integer fieldId);
}
