package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemTempGroupSetting;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 *视图对象 item_temp_group
 */
@Data
@EqualsAndHashCode
@AutoMapper(target = ItemTempGroupSetting.class)
public class ItemTempGroupSettingVo implements   Serializable{
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * group id
     */
    private Integer groupId;

    /**
     * template id
     */
    private Integer templateId;

    /**
     * display order
     */
    private Integer displayOrder;
}
