package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 联系人信息变更日志字段对象 contact_info_changelog_field
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("contact_info_changelog_field")
public class ContactInfoChangelogField extends BaseChangelogField {
    /**
     * 联系人ID
     */
    @TableField(value = "contact_id")
    private Integer contactId;
}
