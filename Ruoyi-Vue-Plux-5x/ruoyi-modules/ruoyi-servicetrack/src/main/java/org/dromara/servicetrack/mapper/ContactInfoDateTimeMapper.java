package org.dromara.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ContactInfoDateTime;
import org.dromara.servicetrack.domain.bo.ContactInfoDateTimeBo;
import org.dromara.servicetrack.domain.vo.ContactInfoDateTimeVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.Date;
import java.util.List;

/**
 * 联系人时间字段管理 数据层
 *
 * <AUTHOR>
 */
public interface ContactInfoDateTimeMapper extends BaseMapperPlus<ContactInfoDateTime, ContactInfoDateTimeVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ContactInfoDateTime> buildWrapper(ContactInfoDateTimeBo bo) {
        LambdaQueryWrapper<ContactInfoDateTime> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ContactInfoDateTime::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, ContactInfoDateTime::getProjectId, bo.getProjectId());
        lqw.eq(bo.getContactId() != null, ContactInfoDateTime::getContactId, bo.getContactId());
        lqw.eq(bo.getFieldId() != null, ContactInfoDateTime::getFieldId, bo.getFieldId());
        lqw.eq(bo.getDateTime() != null, ContactInfoDateTime::getDateTime, bo.getDateTime());
        return lqw;
    }

    /**
     * 根据项目ID和联系人ID查询时间字段列表
     *
     * @param projectId 项目ID
     * @param contactId 联系人ID
     * @return 时间字段列表
     */
    List<ContactInfoDateTimeVo> selectByProjectAndContact(@Param("projectId") Integer projectId, @Param("contactId") Integer contactId);

    /**
     * 根据项目ID查询所有联系人时间字段
     *
     * @param projectId 项目ID
     * @return 时间字段列表
     */
    List<ContactInfoDateTimeVo> selectByProjectId(@Param("projectId") Integer projectId);

    /**
     * 根据字段ID查询时间字段列表
     *
     * @param projectId 项目ID
     * @param contactId 联系人ID
     * @param fieldId   字段ID
     * @return 时间字段列表
     */
    List<ContactInfoDateTimeVo> selectByFieldId(@Param("projectId") Integer projectId, 
                                               @Param("contactId") Integer contactId, 
                                               @Param("fieldId") Integer fieldId);

    /**
     * 根据时间范围查询时间字段列表
     *
     * @param projectId 项目ID
     * @param contactId 联系人ID
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 时间字段列表
     */
    List<ContactInfoDateTimeVo> selectByDateRange(@Param("projectId") Integer projectId, 
                                                 @Param("contactId") Integer contactId, 
                                                 @Param("startDate") Date startDate, 
                                                 @Param("endDate") Date endDate);
}
