package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BaseChangelogField extends  BaseField{

    /**
     * changelog Id
     */
    @TableField(value = "changelog_id")
    private Integer changelogId;

    /**
     * change_from
     */
    @TableField(value = "change_from")
    private String changeFrom;

    /**
     * change_to
     */
    @TableField(value = "change_to")
    private String changeTo;

    /**
     * description
     */
    @TableField(value = "description")
    private String description;
}
