package org.dromara.servicetrack.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ItemTimestamp;
import org.dromara.servicetrack.domain.bo.ItemTimestampBo;
import org.dromara.servicetrack.domain.vo.ItemTimestampVo;

/**
 * item_timestamp表的mapper接口
 */
public interface ItemTimestampMapper extends BaseMapperPlus<ItemTimestamp, ItemTimestampVo> {
    /**
     * 查询item_timestamp表的所有数据
     *
     * @return 返回item_timestamp表的所有数据
     */
    List<ItemTimestampVo> selectAll();

    /**
     * 根据主键查询item_timestamp表的数据
     *
     * @param projectId 主键值
     * @param itemId 主键值
     * @return 返回主键对应的数据
     */
    List<ItemTimestampVo> selectByPk(@Param("projectId") Integer projectId, @Param("itemId") Integer itemId);

    /**
     * 根据项目ID和条目ID获取最大序列号
     *
     * @param projectId 项目ID
     * @param itemId 条目ID
     * @param fieldId 字段ID
     * @return 返回最大序列号
     */
    Integer getMaxSeqNo(@Param("projectId") Integer projectId, @Param("itemId") Integer itemId, @Param("fieldId") Integer fieldId);
}
