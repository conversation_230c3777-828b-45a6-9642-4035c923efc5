package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
/**
 *  Item_temp_groupsetting
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_temp_groupsetting")
public class ItemTempGroupSetting extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * group id
     */
    @TableField(value = "group_id")
    private Integer groupId;

    /**
     * template id
     */
    @TableField(value = "template_id")
    private Integer templateId;

    /**
     * display order
     */
    @TableField(value = "display_order")
    private Integer displayOrder;
}
