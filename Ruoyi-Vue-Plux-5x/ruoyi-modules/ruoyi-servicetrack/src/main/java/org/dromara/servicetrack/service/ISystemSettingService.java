package org.dromara.servicetrack.service;

import org.dromara.servicetrack.domain.bo.SystemSettingBinderBo;
import org.dromara.servicetrack.domain.vo.SystemSettingVo;

import java.util.Collection;
import java.util.List;

/**
 * 系统设置 服务层
 *
 * <AUTHOR> f<PERSON>
 * */
public interface ISystemSettingService {
    List<SystemSettingVo> selectSystemSettingList(List<Integer> settingIds);

    Boolean updateByBo(SystemSettingBinderBo bo);

    Boolean deleteWithValidByIds(Collection<Integer> settingIds, Boolean isValid);
}
