package org.dromara.servicetrack.controller;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectMemberBinderBo;
import org.dromara.common.servicetrack.domain.bo.ProjectMemberBo;
import org.dromara.common.servicetrack.domain.vo.ProjectMemberVo;
import org.dromara.common.servicetrack.service.IProjectMemberService;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目成员管理
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/projectMember")
public class ProjectMemberController extends BaseController {
    private final IProjectMemberService projectMemberService;
    /**
     * 查询项目成员列表
     */
    @GetMapping("/list")
    public TableDataInfo<ProjectMemberVo> list(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        PageQuery pageQuery) {
        return projectMemberService.queryPageList(projectId,pageQuery);
    }
    /**
     * 新增项目成员
     */
    @Log(title = "项目成员管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Boolean> add(@Validated @RequestBody ProjectMemberBinderBo bo) {
        return R.ok(projectMemberService.insertByBo(bo));
    }

    /**
     * 修改项目成员
     */
    @Log(title = "项目成员管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Boolean> edit(@Validated @RequestBody ProjectMemberBinderBo bo) {
        return R.ok(projectMemberService.updateByBo(bo));
    }

    /**
     * 删除项目成员
     *
     * @param ids 主键串
     */
    @Log(title = "项目成员管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectMemberService.deleteWithValidByIds(List.of(ids), true));
    }
}
