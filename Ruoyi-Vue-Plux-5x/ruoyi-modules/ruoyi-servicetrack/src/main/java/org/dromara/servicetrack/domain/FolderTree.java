package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * 文件夹树结构对象 folder_tree
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("folder_tree")
public class FolderTree extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * 项目ID
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * 父文件夹ID
     */
    @TableField(value = "parent_folder")
    private Integer parentFolder;

    /**
     * 子文件夹ID
     */
    @TableField(value = "child_folder")
    private Integer childFolder;

    /**
     * 显示顺序
     */
    @TableField(value = "display_order")
    private Integer displayOrder;
}
