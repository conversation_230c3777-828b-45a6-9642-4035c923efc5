package org.dromara.servicetrack.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * item_amount表的实体类
 *
 * <AUTHOR> fei
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_amount")
public class ItemAmount  extends ItemField {

    /**
     * field_value
     */
    private Double fieldValue;
}
