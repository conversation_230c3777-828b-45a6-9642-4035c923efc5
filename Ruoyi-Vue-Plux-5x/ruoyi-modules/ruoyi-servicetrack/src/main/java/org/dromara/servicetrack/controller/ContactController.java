package org.dromara.servicetrack.controller;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.vo.ListViewFieldVo;
import org.dromara.common.web.core.BaseController;
import org.dromara.servicetrack.domain.bo.ContactInfoBo;
import org.dromara.servicetrack.domain.bo.ContactInfoListBo;
import org.dromara.servicetrack.domain.vo.ContactInfoListVo;
import org.dromara.servicetrack.domain.vo.STUserInfoVo;
import org.dromara.servicetrack.service.IContactInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 联系人管理
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/contact")
public class ContactController extends BaseController {
    private final IContactInfoService contactInfoService;

    /**
     * 查询联系人列表基于customer
     */
    @GetMapping("/list")
    public TableDataInfo<ContactInfoListVo> list(ContactInfoListBo bo, PageQuery pageQuery) {
        return contactInfoService.selectPageUserList(bo, pageQuery);
    }
    /**
     * 根据Project Id和contact Id获得联系人字段详情
     */
    @GetMapping("/getInfo")
    public R<STUserInfoVo> getItemInfo(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "ContactId不能为空") @RequestParam Integer contactId) {
        return R.ok(contactInfoService.getContactInfoDetail(projectId, contactId));
    }
    /**
     * 新增联系人信息
     */
    @Log(title = "联系人信息管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Integer> add(@Validated @RequestBody ContactInfoBo bo) {
        if (!contactInfoService.checkUserNameUnique(bo)) {
            var userNameFieldName = bo.getSpecialFieldName();
            if( userNameFieldName == null || userNameFieldName.isEmpty()){
                userNameFieldName = "用户名";
            }
            return R.fail("新增联系人'" + bo.getUserName() + String.format("'失败，%s已存在",userNameFieldName));
        }
        else if ( !contactInfoService.checkUserPhoneUnique(bo)) {
            return R.fail("新增联系人'" + bo.getUserName() + "'失败，手机号码已存在");
        } else if ( !contactInfoService.checkUserEmailUnique(bo)) {
            return R.fail("新增联系人'" + bo.getUserName() + "'失败，邮箱账号已存在");
        }
        return R.ok(contactInfoService.insertByBo(bo));
    }

    /**
     * 修改联系人信息
     */
    @Log(title = "联系人信息管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Integer> edit(@Validated(EditGroup.class) @RequestBody ContactInfoBo bo) {
        if (!contactInfoService.checkUserNameUnique(bo)) {
            var userNameFieldName = bo.getSpecialFieldName();
            if( userNameFieldName == null || userNameFieldName.isEmpty()){
                userNameFieldName = "用户名";
            }
            return R.fail("修改联系人'" + bo.getUserName() + String.format("'失败，%s已存在",userNameFieldName));
        }
        else if ( !contactInfoService.checkUserPhoneUnique(bo)) {
            return R.fail("修改联系人'" + bo.getUserName() + "'失败，手机号码已存在");
        } else if ( !contactInfoService.checkUserEmailUnique(bo)) {
            return R.fail("修改联系人'" + bo.getUserName() + "'失败，邮箱账号已存在");
        }

        return R.ok(contactInfoService.updateByBo(bo));
    }

    /**
     * 删除联系人信息
     *
     * @param ids 主键串
     */
    @Log(title = "联系人信息管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids,
                          @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                          @NotNull(message = "CustomerId不能为空") @RequestParam Integer customerId) {
        return toAjax(contactInfoService.deleteWithValidByIds(List.of(ids), projectId, customerId,true));
    }

    /**
     * 获取列表Contact字段信息
     */
    @GetMapping("/getListviewFields")
    public R<ListViewFieldVo> getListviewFields(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                @NotNull(message = "Option不能为空") @RequestParam Integer option){
        return R.ok(contactInfoService.getListviewFields(projectId,option));
    }
}
