package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *  userinfo_changelog_field
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("user_info_changelog_field")
public class UserInfoChangelogField extends BaseChangelogField{
    /**
     * user id
     */
    @TableField(value = "user_id")
    private Integer userId;
}
