package org.dromara.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.SystemSetting;
import org.dromara.servicetrack.domain.bo.SystemSettingBo;
import org.dromara.servicetrack.domain.vo.SystemSettingVo;

import java.util.List;

/**
 * 系统设置 数据层
 *
 * <AUTHOR>
 */
public interface SystemSettingMapper extends BaseMapperPlus<SystemSetting, SystemSettingVo> {

    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<SystemSetting> buildWrapper(SystemSettingBo bo) {
        LambdaQueryWrapper<SystemSetting> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, SystemSetting::getId, bo.getId());
        lqw.eq(bo.getSettingId() != null, SystemSetting::getSettingId, bo.getSettingId());
        lqw.like(StringUtils.isNotBlank(bo.getSettingName()), SystemSetting::getSettingName, bo.getSettingName());
        lqw.eq(bo.getSettingOption() != null, SystemSetting::getSettingOption, bo.getSettingOption());
        lqw.eq(bo.getSettingContent() != null, SystemSetting::getSettingContent, bo.getSettingContent());
        return lqw;
    }

    /**
     * 查询系统设置列表
     *
     * @return 系统设置列表
     */
    List<SystemSettingVo> selectSystemSettingList();

    /**
     * 根据设置ID查询系统设置
     *
     * @param settingId 设置ID
     * @return 系统设置
     */
    SystemSettingVo selectBySettingId(@Param("settingId") Integer settingId);

    /**
     * 根据设置IDs列表查询系统设置列表
     *
     * @param settingIds 设置ID列表
     * @return 系统设置列表
     */
    List<SystemSettingVo> selectBySettingIds(@Param("settingIds") List<Integer> settingIds);
    /**
     * 根据设置名称查询系统设置列表
     *
     * @param settingName 设置名称
     * @return 系统设置列表
     */
    List<SystemSettingVo> selectBySettingName(@Param("settingName") String settingName);

    /**
     * 根据设置选项查询系统设置列表
     *
     * @param settingOption 设置选项
     * @return 系统设置列表
     */
    List<SystemSettingVo> selectBySettingOption(@Param("settingOption") Integer settingOption);
}
