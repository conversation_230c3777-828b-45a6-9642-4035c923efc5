package org.dromara.servicetrack.mapper;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ItemInfo;
import org.dromara.servicetrack.domain.bo.ItemInfoBo;
import org.dromara.servicetrack.domain.bo.TrendReportBo;
import org.dromara.servicetrack.domain.vo.DistributionReportVo;
import org.dromara.servicetrack.domain.vo.ItemInfoVo;
import org.dromara.servicetrack.domain.vo.ItemListVo;
import org.dromara.servicetrack.domain.vo.TrendReportVo;

import java.util.List;
import java.util.Map;


/**
 * 条目管理 数据层
 *
 * <AUTHOR> fei
 */
public interface ItemInfoMapper extends BaseMapperPlus<ItemInfo, ItemInfoVo>  {

    default LambdaQueryWrapper<ItemInfo> buildWrapper(ItemInfoBo bo) {
        LambdaQueryWrapper<ItemInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ItemInfo::getId, bo.getId());
        lqw.and(w -> w.eq(ItemInfo::getDelFlag, "0").or().isNull(ItemInfo::getDelFlag));
        lqw.eq(bo.getProjectId() != null, ItemInfo::getProjectId, bo.getProjectId());
        lqw.eq(bo.getItemId() != null, ItemInfo::getItemId, bo.getItemId());
        lqw.like(StringUtils.isNotBlank(bo.getItemTitle()), ItemInfo::getItemTitle, bo.getItemTitle());
        lqw.like(StringUtils.isNotBlank(bo.getDisplayId()), ItemInfo::getDisplayId, bo.getDisplayId());
        lqw.eq(bo.getOwnerId() != null, ItemInfo::getOwnerId, bo.getOwnerId());
        lqw.eq(bo.getStateId() != null, ItemInfo::getStateId, bo.getStateId());
        lqw.eq(bo.getModifiedBy() != null, ItemInfo::getModifiedBy, bo.getModifiedBy());
        lqw.eq(bo.getCreatedBy() != null, ItemInfo::getCreatedBy, bo.getCreatedBy());
        lqw.eq(bo.getTypeId() != null, ItemInfo::getTypeId, bo.getTypeId());
        lqw.eq(bo.getModuleId() != null, ItemInfo::getModuleId, bo.getModuleId());
        return lqw;
    }

    @DataPermission({
        @DataColumn(key = "projectName", value = "u.project_id"),
        @DataColumn(key = "itemTitle", value = "u.item_id")
    })
    Page<ItemListVo> selectPageItemList(@Param("page") Page<ItemInfo> page, @Param(Constants.WRAPPER) Wrapper<ItemInfo> queryWrapper,
                                        @Param("projectId") Integer projectId, @Param("itemSqlSegment") String itemSqlSegment,
                                        @Param("sortFieldId") Integer sortFieldId, @Param("textFieldIds") List<Integer> textFieldIds,
                                        @Param("dateTimeFieldIds") List<Integer> dateTimeFieldIds,@Param("selectionFieldIds") List<Integer> selectionFieldIds,
                                        @Param("amountFieldIds") List<Integer> amountFieldIds);
    /**
     * 获取条目详情
     *
     * @param projectId 项目id
     * @param itemId    条目id
     * @return 条目详情
     */
    ItemInfoVo getItemDetail(@Param("projectId") Integer projectId, @Param("itemId") Integer itemId);

    /**
     * 获取分布图
     *
     * @param projectId 项目id
     * @param distributionFieldId 分布字段id
     * @return 分布图
     */
    List<DistributionReportVo> getDistributionReport(@Param("projectId") Integer projectId, @Param("distributionFieldId") Integer distributionFieldId,@Param("itemSqlSegment") String itemSqlSegment);

    /**
     * 获取趋势图
     *
     * @param projectId 项目id
     * @param trendFieldId 趋势字段id
     * @return 趋势图
     */
    List<TrendReportVo> getTrendReport(@Param("projectId") Integer projectId, @Param("trendFieldId") Integer trendFieldId, @Param("itemSqlSegment") String itemSqlSegment);
}
