package org.dromara.servicetrack.service;

import org.dromara.servicetrack.domain.bo.UserSettingBinderBo;
import org.dromara.servicetrack.domain.bo.UserSettingBo;
import org.dromara.servicetrack.domain.vo.UserSettingVo;

import java.util.Collection;
import java.util.List;

public interface IUserSettingService {
    List<UserSettingVo> selectUserSettingList(Integer projectId, Integer userId,List<Integer> settingIds);
    Boolean updateUserSetting(UserSettingBinderBo bo);
    Boolean deleteWithValidByIds(Collection<Long> keyIds, Boolean isValid);;
}
