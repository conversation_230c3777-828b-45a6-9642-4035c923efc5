package org.dromara.servicetrack.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.servicetrack.domain.FolderInfo;
import org.dromara.servicetrack.domain.ReportInfo;
import org.dromara.servicetrack.domain.bo.FolderInfoBo;
import org.dromara.servicetrack.domain.bo.FolderTreeBo;
import org.dromara.servicetrack.domain.vo.FolderTreeVo;
import org.dromara.servicetrack.domain.vo.ReportInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.dromara.servicetrack.mapper.FolderInfoMapper;
import org.dromara.servicetrack.mapper.FolderTreeMapper;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.servicetrack.mapper.ReportInfoMapper;
import org.dromara.servicetrack.service.IReportTreeService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 报告树结构管理Service业务层处理
 * 继承文件夹树服务，提供报告相关的树结构功能
 *
 * <AUTHOR> Fei
 */
@Slf4j
@Primary
@Service
public class ReportTreeServiceImpl extends FolderTreeServiceImpl implements IReportTreeService {

    private final ReportInfoMapper reportInfoMapper;

    @Autowired
    public ReportTreeServiceImpl(FolderTreeMapper baseMapper, FolderInfoMapper folderInfoMapper, ReportInfoMapper reportInfoMapper, TableSequenceManager tableSequenceManager) {
        super(baseMapper, folderInfoMapper,tableSequenceManager);
        this.reportInfoMapper = reportInfoMapper;
    }

    /**
     * 根据项目ID获取报告文件夹树结构
     * 只包含包含报告的文件夹
     */
    @Override
    public List<Tree<Integer>> getReportFolderTreeByProjectId(Integer projectId) {
        return getReportFolderTreeByProjectId(projectId, null);
    }

    /**
     * 根据项目ID和根文件夹ID获取报告文件夹树结构
     * 只包含包含报告的文件夹
     */
    @Override
    public List<Tree<Integer>> getReportFolderTreeByProjectId(Integer projectId, Integer rootId) {
        // 1. 获取完整的文件夹树结构（使用父类方法）
        List<Tree<Integer>> fullTree = super.getFolderTreeByProjectId(projectId, rootId);

        if (CollUtil.isEmpty(fullTree)) {
            return CollUtil.newArrayList();
        }

        // 2. 获取包含报告的文件夹ID集合
        Set<Integer> foldersWithReports = getFoldersWithReports(projectId);

        if (CollUtil.isEmpty(foldersWithReports)) {
            return CollUtil.newArrayList();
        }

        // 3. 过滤树结构，只保留包含报告的文件夹及其父级路径
        return filterTreeByReportFolders(fullTree, foldersWithReports);
    }

    @Override
    public List<FolderTreeVo> getChildFoldersByParentIdWithReports(Integer projectId, Integer parentFolderId) {
        var childFolders = super.getChildFoldersByParentId(projectId, parentFolderId);
        if (CollUtil.isNotEmpty(childFolders)) {
            var reportsWithParentFolders = getReportsWithParentFolders(projectId, childFolders.stream()
                .map(FolderTreeVo::getChildFolder)
                .collect(Collectors.toList()));
            if (CollUtil.isNotEmpty(reportsWithParentFolders)) {
                childFolders.forEach(folder -> {
                    var reports = reportsWithParentFolders.stream()
                        .filter(oneReport -> oneReport.getFolderId().equals(folder.getChildFolder())).toList();
                    if( !reports.isEmpty()) {
                        for(var report : reports){
                            if( folder.getChildren() == null)
                                folder.setChildren(new ArrayList<>());
                            var reportFolder = new FolderTreeVo();
                            reportFolder.setChildFolder(report.getReportId());
                            var reportTypeId = report.getReportType() != null ? (report.getReportType() * -1) : 0;
                            reportFolder.setChildFolderType(reportTypeId);
                            reportFolder.setChildFolderName(report.getReportName());
                            folder.getChildren().add(reportFolder);
                        }
                    }
                });
            }
            return childFolders;
        }
        return List.of();
    }

    /**
     * 获取指定文件夹下的报告数量
     */
    @Override
    public Integer getReportCountByFolderId(Integer projectId, Integer folderId) {
        LambdaQueryWrapper<org.dromara.servicetrack.domain.ReportInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(org.dromara.servicetrack.domain.ReportInfo::getProjectId, projectId);
        lqw.eq(org.dromara.servicetrack.domain.ReportInfo::getFolderId, folderId);
        return Math.toIntExact(reportInfoMapper.selectCount(lqw));
    }

    /**
     * 检查文件夹是否包含报告（包括子文件夹）
     */
    @Override
    public Boolean hasReportsInFolder(Integer projectId, Integer folderId) {
        // 1. 检查当前文件夹是否有报告
        if (getReportCountByFolderId(projectId, folderId) > 0) {
            return true;
        }

        // 2. 获取子文件夹列表（使用父类方法）
        List<org.dromara.servicetrack.domain.vo.FolderTreeVo> childFolders =
            super.getChildFoldersByParentId(projectId, folderId);

        if (CollUtil.isEmpty(childFolders)) {
            return false;
        }

        // 3. 递归检查子文件夹
        for (org.dromara.servicetrack.domain.vo.FolderTreeVo childFolder : childFolders) {
            if (hasReportsInFolder(projectId, childFolder.getChildFolder())) {
                return true;
            }
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertByBo(FolderInfoBo bo) {
        return super.insertByBo(bo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Integer projectId, List<Integer> folderIds, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }

        //check if they have reports
        var reportIds = reportInfoMapper.selectList(Wrappers.<ReportInfo>lambdaQuery()
            .eq(ReportInfo::getProjectId, projectId)
            .in(ReportInfo::getFolderId, folderIds))
            .stream().map(ReportInfo::getReportId).toList();
        if( !reportIds.isEmpty()){
            throw new ServiceException("有些文件夹下有报告,不能删除！");
        }

        return super.deleteWithFolderIds(projectId, folderIds);
    }

    /**
     * 获取包含报告的文件夹ID集合
     */
    private Set<Integer> getFoldersWithReports(Integer projectId) {
        LambdaQueryWrapper<ReportInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(ReportInfo::getProjectId, projectId);
        lqw.isNotNull(ReportInfo::getFolderId);
        lqw.select(ReportInfo::getFolderId);

        return reportInfoMapper.selectList(lqw).stream()
            .map(ReportInfo::getFolderId)
            .collect(Collectors.toSet());
    }
    private List<ReportInfoVo> getReportsWithParentFolders(Integer projectId, List<Integer> parentFolderIds) {
        LambdaQueryWrapper<ReportInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(ReportInfo::getProjectId, projectId);
        lqw.isNotNull(ReportInfo::getFolderId);
        if (parentFolderIds != null && !parentFolderIds.isEmpty()) {
            lqw.in(ReportInfo::getFolderId, parentFolderIds);
        }
        lqw.select(ReportInfo::getFolderId, ReportInfo::getReportId, ReportInfo::getReportName,ReportInfo::getReportType);

        return reportInfoMapper.selectVoList(lqw);
    }

    /**
     * 过滤树结构，只保留包含报告的文件夹及其父级路径
     */
    private List<Tree<Integer>> filterTreeByReportFolders(List<Tree<Integer>> trees, Set<Integer> foldersWithReports) {
        return trees.stream()
            .map(tree -> filterSingleTree(tree, foldersWithReports))
            .filter(tree -> tree != null)
            .collect(Collectors.toList());
    }

    /**
     * 过滤单个树节点
     */
    private Tree<Integer> filterSingleTree(Tree<Integer> tree, Set<Integer> foldersWithReports) {
        // 递归过滤子节点
        List<Tree<Integer>> filteredChildren = CollUtil.newArrayList();
        if (tree.getChildren() != null) {
            for (Tree<Integer> child : tree.getChildren()) {
                Tree<Integer> filteredChild = filterSingleTree(child, foldersWithReports);
                if (filteredChild != null) {
                    filteredChildren.add(filteredChild);
                }
            }
        }

        // 判断当前节点是否应该保留
        boolean shouldKeep = foldersWithReports.contains(tree.getId()) || !filteredChildren.isEmpty();

        if (shouldKeep) {
            // 创建新的树节点
            Tree<Integer> newTree = new Tree<>();
            newTree.setId(tree.getId());
            newTree.setParentId(tree.getParentId());
            newTree.setName(tree.getName());
            newTree.setWeight(tree.getWeight());
            newTree.setChildren(filteredChildren);
            return newTree;
        }

        return null;
    }
}
