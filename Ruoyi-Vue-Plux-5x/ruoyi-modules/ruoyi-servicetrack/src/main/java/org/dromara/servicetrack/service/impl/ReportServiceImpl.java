package org.dromara.servicetrack.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.servicetrack.domain.bo.DistributionReportBo;
import org.dromara.servicetrack.domain.bo.TrendReportBo;
import org.dromara.servicetrack.domain.vo.DistributionReportVo;
import org.dromara.servicetrack.domain.vo.TrendReportVo;
import org.dromara.servicetrack.mapper.ItemInfoMapper;
import org.dromara.servicetrack.service.IItemInfoService;
import org.dromara.servicetrack.service.IReportService;
import org.dromara.common.servicetrack.constant.eFieldTypeDef;
import org.springframework.stereotype.Service;

import java.time.temporal.ChronoField;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报告管理 服务层实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReportServiceImpl implements IReportService {
    private final IItemInfoService itemInfoService;
    private final ItemInfoMapper itemInfoMapper;

    public static final int DATE_RANGE_OPTION_DAILY = 1;
    public static final int DATE_RANGE_OPTION_WEEKLY = 2;
    public static final int DATE_RANGE_OPTION_MONTHLY = 3;
    /*
     * 分布式报告
     */
    @Override
    public List<DistributionReportVo> getDistributionReport(DistributionReportBo bo) {
        if(bo == null){
            throw new ServiceException("DistributionReportBo is null");
        }
        if( bo.getDistributionFieldId() == null || bo.getDistributionFieldId() == 0){
            throw new ServiceException("Distribution Field Id can't be 0");
        }
        var distributionFieldType = ProjectManager.getInstance(bo.getProjectId()).getFieldType(bo.getDistributionFieldId());
        if( distributionFieldType == null){
            throw new ServiceException("Distribution Field Type can't be null");
        }
        if( distributionFieldType != eFieldTypeDef.Dropdown.getValue()){
            throw new ServiceException("Distribution Field Type must be dropdown");
        }
        var queryWrapper = itemInfoService.buildQueryWrapper(bo);

        var list = itemInfoMapper.getDistributionReport(bo.getProjectId(), bo.getDistributionFieldId(), bo.getSqlSegment());
        if( list  != null && !list.isEmpty()) {
            list.forEach(item -> item.setChoiceName(ProjectManager.getInstance(bo.getProjectId()).getChoiceName(bo.getDistributionFieldId(), item.getChoiceId())));
        }
        return list;
    }

    /*
     * 趋势报告
     */
    @Override
    public List<TrendReportVo> getTrendReport(TrendReportBo bo) {
        if(bo == null){
            throw new ServiceException("TrendReportBo is null");
        }
        if(bo.getDateFieldId() == null || bo.getDateFieldId() == 0){
            throw new ServiceException("Date Field Id can't be 0");
        }
        var queryWrapper = itemInfoService.buildQueryWrapper(bo);
        var trendReport = itemInfoMapper.getTrendReport(bo.getProjectId(), bo.getDateFieldId(), bo.getSqlSegment());
        if (bo.getDateRangeOption() != null && bo.getDateRangeOption() > DATE_RANGE_OPTION_DAILY) {
            if (bo.getDateRangeOption() == DATE_RANGE_OPTION_WEEKLY) {
                trendReport = getTrendReportByWeekly(bo, trendReport);
            } else if (bo.getDateRangeOption() == DATE_RANGE_OPTION_MONTHLY) {
                trendReport = getTrendReportByMonthly(trendReport);
            }
        }
        return trendReport;
    }

    private static List<TrendReportVo> getTrendReportByMonthly(List<TrendReportVo> trendReport) {
        trendReport = trendReport.stream()
            .collect(Collectors.groupingBy(
                vo -> {
                    java.time.LocalDate localDate = vo.getTrendDate().toInstant()
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDate();
                    return localDate.getYear() + "-" + localDate.getMonthValue();
                },
                Collectors.summingInt(TrendReportVo::getItemCount)
            ))
            .entrySet().stream()
            .sorted((e1, e2) -> {
                String[] parts1 = e1.getKey().split("-");
                String[] parts2 = e2.getKey().split("-");
                int yearCompare = Integer.parseInt(parts1[0]) - Integer.parseInt(parts2[0]);
                if (yearCompare != 0) return yearCompare;
                return Integer.parseInt(parts1[1]) - Integer.parseInt(parts2[1]);
            })
            .map(e -> new TrendReportVo(e.getKey(), e.getValue()))
            .collect(Collectors.toList());
        ;
        return trendReport;
    }

    private List<TrendReportVo> getTrendReportByWeekly(TrendReportBo bo, List<TrendReportVo> trendReport) {
        // 获取时间范围参数
        Map<String, Object> params = bo.getParams();
        Object beginTimeObj = params.get("beginTime");
        Object endTimeObj = params.get("endTime");
        Date beginTime = null;
        Date endTime = null;

        // 处理可能的字符串类型日期
        if (beginTimeObj != null) {
            if (beginTimeObj instanceof Date) {
                beginTime = (Date) beginTimeObj;
            } else {
                beginTime = DateUtils.parseDate(beginTimeObj.toString());
            }
        }

        if (endTimeObj != null) {
            if (endTimeObj instanceof Date) {
                endTime = (Date) endTimeObj;
            } else {
                endTime = DateUtils.parseDate(endTimeObj.toString());
            }
        }
        if (beginTime != null && endTime != null) {
            java.time.LocalDate beginDate = beginTime.toInstant()
                .atZone(java.time.ZoneId.systemDefault())
                .toLocalDate();
            java.time.LocalDate endDate = endTime.toInstant()
                .atZone(java.time.ZoneId.systemDefault())
                .toLocalDate();

            // 计算时间范围内的总周数
            long totalWeeks = java.time.temporal.ChronoUnit.WEEKS.between(beginDate, endDate) + 1;

            // 先创建一个完整周序列，初始化为0
            List<TrendReportVo> allWeeks = new java.util.ArrayList<>();
            for (int i = 0; i < totalWeeks; i++) {
                allWeeks.add(new TrendReportVo("Week " + (i + 1), 0));
            }

            // 如果有实际数据，则更新对应周的计数
            if (!trendReport.isEmpty()) {
                // 按日期排序
                trendReport = trendReport.stream()
                    .sorted((a, b) -> a.getTrendDate().compareTo(b.getTrendDate()))
                    .collect(Collectors.toList());

                // 第一个数据的日期作为起始日期
                java.util.Date firstDate = trendReport.get(0).getTrendDate();
                java.time.LocalDate firstLocalDate = firstDate.toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDate();

                // 按周分组统计
                Map<String, Integer> weeklyData = trendReport.stream()
                    .collect(Collectors.groupingBy(
                        vo -> {
                            java.time.LocalDate localDate = vo.getTrendDate().toInstant()
                                .atZone(java.time.ZoneId.systemDefault())
                                .toLocalDate();
                            long week = java.time.temporal.ChronoUnit.WEEKS.between(firstLocalDate, localDate) + 1;
                            return "Week " + week;
                        },
                        Collectors.summingInt(TrendReportVo::getItemCount)
                    ));

                // 更新对应周的计数
                for (Map.Entry<String, Integer> entry : weeklyData.entrySet()) {
                    String weekLabel = entry.getKey();
                    Integer count = entry.getValue();

                    // 从weekLabel中提取周数
                    if (weekLabel.startsWith("Week ")) {
                        try {
                            int weekNumber = Integer.parseInt(weekLabel.substring(5));
                            if (weekNumber >= 1 && weekNumber <= allWeeks.size()) {
                                allWeeks.get(weekNumber - 1).setItemCount(count);
                            }
                        } catch (NumberFormatException e) {
                            // 忽略解析错误
                        }
                    }
                }
            }

            trendReport = allWeeks;
        } else {
            // 如果没有时间范围参数，按原始逻辑处理
            if (!trendReport.isEmpty()) {
                trendReport = trendReport.stream()
                    .sorted((a, b) -> a.getTrendDate().compareTo(b.getTrendDate()))
                    .collect(Collectors.toList());

                java.util.Date firstDate = trendReport.get(0).getTrendDate();
                java.time.LocalDate firstLocalDate = firstDate.toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDate();

                // 按周分组统计
                Map<String, Integer> weeklyData = trendReport.stream()
                    .collect(Collectors.groupingBy(
                        vo -> {
                            java.time.LocalDate localDate = vo.getTrendDate().toInstant()
                                .atZone(java.time.ZoneId.systemDefault())
                                .toLocalDate();
                            long week = java.time.temporal.ChronoUnit.WEEKS.between(firstLocalDate, localDate) + 1;
                            return "Week " + week;
                        },
                        Collectors.summingInt(TrendReportVo::getItemCount)
                    ));

                // 确定最大周数
                int maxWeek = weeklyData.keySet().stream()
                    .mapToInt(key -> {
                        if (key.startsWith("Week ")) {
                            try {
                                return Integer.parseInt(key.substring(5));
                            } catch (NumberFormatException e) {
                                return 0;
                            }
                        }
                        return 0;
                    })
                    .max()
                    .orElse(0);

                // 创建完整周序列
                List<TrendReportVo> allWeeks = new java.util.ArrayList<>();
                for (int i = 1; i <= maxWeek; i++) {
                    String weekLabel = "Week " + i;
                    int count = weeklyData.getOrDefault(weekLabel, 0);
                    allWeeks.add(new TrendReportVo(weekLabel, count));
                }

                trendReport = allWeeks;
            }
        }
        return trendReport;
    }
}
