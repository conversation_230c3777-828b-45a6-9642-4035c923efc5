package org.dromara.servicetrack.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图对象 base change text
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class BaseChangeTextVo extends BaseFieldVo{
    /**
     * user_id
     */
    private Integer userId;
    /**
     * changelog_id
     */
    private Integer changelogId;

    /**
     * change from
     */
    private String changeFrom;

    /**
     * change to
     */
    private String changeTo;
}
