package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.servicetrack.domain.vo.ProjectSettingInCacheVo;
import org.dromara.servicetrack.domain.ProjectSetting;

import java.io.Serial;
import java.io.Serializable;

@Data
@AutoMapper(target = ProjectSetting.class)
public class ProjectSettingVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    /*
     *项目id
     */
    private Integer projectId;
    /*设置id
     */
    private Integer settingId;
    /*
     *设置名称
     */
    private String settingName;
    /*
     *设置选项
     */
    private Integer settingOption;

    /*
     *设置内容
     */
    private String settingContent;

    public ProjectSettingInCacheVo convertToProjectSettingInCacheVo() {
        ProjectSettingInCacheVo vo = new ProjectSettingInCacheVo();
        vo.setId(this.id);
        vo.setProjectId(this.projectId);
        vo.setSettingId(this.settingId);
        vo.setSettingName(this.settingName);
        vo.setSettingOption(this.settingOption);
        vo.setSettingContent(this.settingContent);
        return vo;
    }
}
