package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.UserInfoChangelogField;

/**
 * 视图对象 user info changelog
 */
@Data
@EqualsAndHashCode(callSuper =true)
@AutoMapper(target = UserInfoChangelogField.class)
public class UserInfoChangelogFieldVo extends BaseChangelogFieldVo{
    /**
     * user id
     */
    private Integer userId;

    /**
     * user name
     */
    private String userName;
}
