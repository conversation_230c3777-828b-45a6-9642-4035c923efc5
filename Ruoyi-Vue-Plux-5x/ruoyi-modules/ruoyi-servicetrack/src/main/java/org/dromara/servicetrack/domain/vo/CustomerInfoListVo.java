package org.dromara.servicetrack.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.CustomerInfo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 客户信息ListView视图对象 customer info
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CustomerInfo.class)
public class CustomerInfoListVo extends CustomFieldsVo implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 客户ID
     */
    private Integer customerId;

    /**
     * 客户名称
     */
    @JsonIgnore
    private String customerName;

    /**
     * 客户状态
     */
    @JsonIgnore
    private String customerStatus;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createdTime;
    /**
     * 创建者
     */
    @JsonIgnore
    private Integer createdBy;

    /**
     * 修改者
     */
    @JsonIgnore
    private Integer modifiedBy;
    /**
     * 修改时间
     */
    @JsonIgnore
    private Date modifiedTime;

    @JsonIgnore
    private Integer customerType;
    @JsonIgnore
    private String customerTypeName;
    @JsonIgnore
    private Integer customerIndustry;
    @JsonIgnore
    private String customerIndustryName;
    @JsonIgnore
    private Integer customerLevel;
    @JsonIgnore
    private String customerLevelName;
    @JsonIgnore
    private String customerAddress;
    /**
     * 客户字段数据
     */
    private List<ListFieldVo> values;
}
