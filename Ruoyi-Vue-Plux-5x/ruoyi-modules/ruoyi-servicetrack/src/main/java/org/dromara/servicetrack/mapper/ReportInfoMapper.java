package org.dromara.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ReportInfo;
import org.dromara.servicetrack.domain.bo.ReportInfoBo;
import org.dromara.servicetrack.domain.vo.ReportInfoVo;

import java.util.List;

/**
 * 报告信息管理 数据层
 *
 * <AUTHOR>
 */
public interface ReportInfoMapper extends BaseMapperPlus<ReportInfo, ReportInfoVo> {

    /**
     * 根据项目ID和文件夹ID查询报告列表
     *
     * @param projectId 项目ID
     * @param folderId 文件夹ID
     * @return 报告信息列表
     */
    List<ReportInfoVo> getReportListByProjectIdAndFolderId(@Param("projectId") Integer projectId, @Param("folderId") Integer folderId);

    /**
     * 根据项目ID和报告ID查询报告信息
     *
     * @param projectId 项目ID
     * @param reportId 报告ID
     * @return 报告信息
     */
    ReportInfoVo getReportInfo(@Param("projectId") Integer projectId, @Param("reportId") Integer reportId);

    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ReportInfo> buildWrapper(ReportInfoBo bo) {
        LambdaQueryWrapper<ReportInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ReportInfo::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, ReportInfo::getProjectId, bo.getProjectId());
        lqw.eq(bo.getReportId() != null, ReportInfo::getReportId, bo.getReportId());
        lqw.eq(bo.getReportType() != null, ReportInfo::getReportType, bo.getReportType());
        lqw.eq(bo.getTargetProjectId() != null, ReportInfo::getTargetProjectId, bo.getTargetProjectId());
        lqw.eq(bo.getFolderId() != null, ReportInfo::getFolderId, bo.getFolderId());
        lqw.like(StringUtils.isNotBlank(bo.getReportName()), ReportInfo::getReportName, bo.getReportName());
        lqw.like(StringUtils.isNotBlank(bo.getReportDescription()), ReportInfo::getReportDescription, bo.getReportDescription());
        lqw.eq(bo.getCreatedDate() != null, ReportInfo::getCreatedDate, bo.getCreatedDate());
        lqw.eq(bo.getCreatedBy() != null, ReportInfo::getCreatedBy, bo.getCreatedBy());
        lqw.eq(bo.getModifiedDate() != null, ReportInfo::getModifiedDate, bo.getModifiedDate());
        lqw.eq(bo.getModifiedBy() != null, ReportInfo::getModifiedBy, bo.getModifiedBy());

        return lqw;
    }
}
