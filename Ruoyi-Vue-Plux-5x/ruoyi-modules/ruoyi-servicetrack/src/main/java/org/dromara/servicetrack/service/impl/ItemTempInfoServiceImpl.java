package org.dromara.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.servicetrack.constant.eSTUserTypeDef;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.servicetrack.domain.ItemInfo;
import org.dromara.servicetrack.domain.vo.ItemTempInfoListVo;
import org.dromara.servicetrack.mapper.ItemInfoMapper;
import org.dromara.servicetrack.mapper.ItemTempInfoMapper;
import org.dromara.servicetrack.mapper.UserInfoMapper;
import org.dromara.servicetrack.service.IItemTempInfoService;
import org.dromara.servicetrack.service.IUserSettingService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 条目模板管理 服务层实现
 *
 * <AUTHOR> <PERSON>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ItemTempInfoServiceImpl implements IItemTempInfoService {
    private final ItemTempInfoMapper itemTempInfoMapper;
    private final UserInfoMapper userInfoMapper;
    private final IUserSettingService userSettingService;
    private final ItemInfoMapper itemInfoMapper;

    /**
     * 查询条目模板管理列表
     * @param projectId 项目id
     * @param groupId  组Id
     * @param needItemType  需要条目类型
     * @return 条目模板管理集合
     */
    @Override
    public List<ItemTempInfoListVo> getItemTempInfoList(Integer projectId, Integer groupId, Integer needItemType,Integer filterType) {
        var baseProjectId = ProjectManager.getInstance(projectId).getBaseProjectId();
//        LoginUser loginUser = LoginHelper.getLoginUser();
//        if(loginUser == null)
//            throw new RuntimeException("登录用户不存在!");
//         Long userId = loginUser.getUserId();
//         Integer externalUserId = loginUser.getExternalUserId();
//         Integer stUserType = userInfoMapper.getSTUserTypeById(userId);
//         if(stUserType == null || stUserType == 0)
//             stUserType = eSTModuleType.SPEnabled.getMask();
//         if( eSTModuleType.isBothEPAndSPEnabled(stUserType)){
//             var userSetting = userSettingService.selectUserSettingList(projectId, externalUserId, List.of(eUserSetting.Login_Select_Default_ST_UserType.getValue()));
//             if(userSetting != null && !userSetting.isEmpty()){
//                 stUserType = userSetting.get(0).getSettingOption();
//             }
//             else{
//                 stUserType = eSTModuleType.SPEnabled.getMask();
//             }
//         }
        if( eSTUserTypeDef.isBothEPAndSPEnabled(filterType))
            filterType = 0;
        var itemTempInfoList = itemTempInfoMapper.getItemTempInfoList(baseProjectId, projectId, groupId, filterType);
        if(needItemType == 1){
            var templateIds =  itemTempInfoList.stream().map(ItemTempInfoListVo::getTemplateId).toList();
            if(!templateIds.isEmpty()) {
                var tempInfos = itemInfoMapper.selectVoList(new LambdaQueryWrapper<ItemInfo>().eq(ItemInfo::getProjectId, baseProjectId).in(ItemInfo::getItemId, templateIds));
                for (var tempInfo : tempInfos) {
                    itemTempInfoList.stream().filter(itemTempInfo -> itemTempInfo.getTemplateId().equals(tempInfo.getItemId()))
                        .findFirst()
                        .ifPresent(matchedItemTempInfo -> matchedItemTempInfo.setItemType(tempInfo.getTypeId()));
                }
            }
        }
        return itemTempInfoList;
    }
}
