package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *  Item_temp_info
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_temp_info")
public class ItemTempInfo extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * work project id
     */
    @TableField(value = "work_project_id")
    private Integer workProjectId;
    /**
     * template id
     */
    @TableField(value = "template_id")
    private Integer templateId;

    /**
     * template name
     */
    @TableField(value = "template_name")
    private String templateName;

    /**
     * template description
     */
    @TableField(value = "template_desc")
    private String templateDes;

    /**
     * template type
     */
    @TableField(value = "template_type")
    private Integer templateType;
}
