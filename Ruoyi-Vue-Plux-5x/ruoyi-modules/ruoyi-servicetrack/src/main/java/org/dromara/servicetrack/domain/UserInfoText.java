package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *  user_info_Text
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("user_info_text")
public class UserInfoText extends UserInfoField{
    /**
     * text
     */
    private String text;
}
