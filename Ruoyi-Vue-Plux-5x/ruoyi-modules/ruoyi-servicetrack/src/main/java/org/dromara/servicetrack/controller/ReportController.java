package org.dromara.servicetrack.controller;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.servicetrack.domain.bo.*;
import org.dromara.servicetrack.domain.vo.*;
import org.dromara.servicetrack.service.IItemInfoService;
import org.dromara.servicetrack.service.IReportInfoService;
import org.dromara.servicetrack.service.IReportService;
import org.dromara.servicetrack.service.IReportTreeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 报表管理
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/report")
public class ReportController extends BaseController {
    private final IItemInfoService itemInfoService;
    private final IReportInfoService reportInfoService;
    private final IReportTreeService reportTreeService;
    private final IReportService reportService;

    // start of report
    /**
     * 查询todo列表
     */
    @GetMapping("/todolist")
    public R<ItemToDoListVo> getToDolist(ItemToDoListBo bo, PageQuery pageQuery) {
        return R.ok(itemInfoService.selectPageItemToDoList(bo, pageQuery));
    }
    /**
     * 获取趋势报告
     */
    @GetMapping("/distribution")
    public R<List<DistributionReportVo>> getDistributionReport(DistributionReportBo bo) {
        return R.ok(reportService.getDistributionReport(bo));
    }
    @GetMapping("/trend")
    public R<List<TrendReportVo>> getTrendReport(TrendReportBo bo) {
        return R.ok(reportService.getTrendReport(bo));
    }
   //end of report


    /**
     * 获取报告信息
     */
    @GetMapping("/info")
    public R<ReportInfoVo> getReportInfo(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                         @NotNull(message = "ReportId不能为空") @RequestParam Integer reportId) {
        return R.ok(reportInfoService.getReportInfo(projectId, reportId));
    }
    /**
     * 新增报告
     */
    @Log(title = "reportInfo管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/info")
    public R<Integer> addReportInfo(@Validated @RequestBody ReportInfoBo bo) {
        return R.ok(reportInfoService.insertByBo(bo));
    }
    /**
     * 修改报告
     */
    @Log(title = "reportInfo管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/info")
    public R<Integer> editReportInfo(@Validated @RequestBody ReportInfoBo bo) {
        return R.ok(reportInfoService.updateByBo(bo));
    }

    /**
     * 删除报告
     *
     * @param ids 主键串
     */
    @Log(title = "reportInfo管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/info/{ids}")
    public R<Void> removeReportInfos(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(reportInfoService.deleteWithValidByIds(List.of(ids), true));
    }
}
