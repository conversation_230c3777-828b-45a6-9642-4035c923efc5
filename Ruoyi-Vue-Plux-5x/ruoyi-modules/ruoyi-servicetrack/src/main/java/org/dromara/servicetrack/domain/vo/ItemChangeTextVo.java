package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemChangeText;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 视图对象 item_changetext
 */

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemChangeText.class)
public class ItemChangeTextVo extends BaseChangeTextVo {
    /**
     * item id
     */
    private Integer itemId;
}
