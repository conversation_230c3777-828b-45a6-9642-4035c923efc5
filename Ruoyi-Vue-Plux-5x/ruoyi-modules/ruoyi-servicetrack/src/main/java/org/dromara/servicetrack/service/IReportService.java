package org.dromara.servicetrack.service;

import org.dromara.servicetrack.domain.bo.DistributionReportBo;
import org.dromara.servicetrack.domain.bo.TrendReportBo;
import org.dromara.servicetrack.domain.vo.DistributionReportVo;
import org.dromara.servicetrack.domain.vo.TrendReportVo;

import java.util.List;

/*
 * 报告管理 服务层
 *
 * author <PERSON>
 */
public interface IReportService {

    List<DistributionReportVo> getDistributionReport(DistributionReportBo bo);

    List<TrendReportVo> getTrendReport(TrendReportBo bo);
}
