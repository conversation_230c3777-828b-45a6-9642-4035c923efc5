package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.servicetrack.domain.CustomerContact;

import java.io.Serial;
import java.io.Serializable;

/**
 * 客户联系人关联视图对象 customer_contact
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = CustomerContact.class)
public class CustomerContactVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 客户ID
     */
    private Integer customerId;

    /**
     * 联系人ID
     */
    private Integer contactId;

    /**
     * 客户名称（扩展字段，用于显示）
     */
    private String customerName;

    /**
     * 联系人名称（扩展字段，用于显示）
     */
    private String contactName;

    /**
     * 联系人邮箱（扩展字段，用于显示）
     */
    private String contactEmail;

    /**
     * 联系人电话（扩展字段，用于显示）
     */
    private String contactPhone;
}
