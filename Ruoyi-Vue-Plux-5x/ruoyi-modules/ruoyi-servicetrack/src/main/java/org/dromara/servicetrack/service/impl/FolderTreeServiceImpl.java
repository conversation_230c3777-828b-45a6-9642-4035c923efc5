package org.dromara.servicetrack.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.utils.ValueConvert;
import org.dromara.servicetrack.domain.FolderInfo;
import org.dromara.servicetrack.domain.ReportInfo;
import org.dromara.servicetrack.domain.bo.FolderInfoBo;
import org.springframework.stereotype.Service;
import org.dromara.servicetrack.domain.FolderTree;
import org.dromara.servicetrack.domain.bo.FolderTreeBo;
import org.dromara.servicetrack.domain.vo.FolderTreeVo;
import org.dromara.servicetrack.domain.vo.FolderTreeNodeVo;
import org.dromara.servicetrack.mapper.FolderTreeMapper;
import org.dromara.servicetrack.mapper.FolderInfoMapper;
import org.dromara.servicetrack.service.IFolderTreeService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 文件夹树结构管理Service业务层处理
 *
 * <AUTHOR> Fei
 */
@Service
public class FolderTreeServiceImpl implements IFolderTreeService {

    protected final FolderTreeMapper baseMapper;
    protected final FolderInfoMapper folderInfoMapper;
    private final TableSequenceManager tableSequenceManager;
    /**
     * 构造函数
     */
    public FolderTreeServiceImpl(FolderTreeMapper baseMapper, FolderInfoMapper folderInfoMapper, TableSequenceManager tableSequenceManager) {
        this.baseMapper = baseMapper;
        this.folderInfoMapper = folderInfoMapper;
        this.tableSequenceManager = tableSequenceManager;
    }

    /**
     * 查询文件夹树结构
     */
    @Override
    public FolderTreeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询文件夹树结构列表
     */
    @Override
    public List<FolderTreeVo> queryList(FolderTreeBo bo) {
        LambdaQueryWrapper<FolderTree> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FolderTree> buildQueryWrapper(FolderTreeBo bo) {
        LambdaQueryWrapper<FolderTree> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, FolderTree::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, FolderTree::getProjectId, bo.getProjectId());
        lqw.eq(bo.getParentFolder() != null, FolderTree::getParentFolder, bo.getParentFolder());
        lqw.eq(bo.getChildFolder() != null, FolderTree::getChildFolder, bo.getChildFolder());
        lqw.eq(bo.getDisplayOrder() != null, FolderTree::getDisplayOrder, bo.getDisplayOrder());
        lqw.orderByAsc(FolderTree::getDisplayOrder);
        return lqw;
    }

    /**
     * 新增文件夹树结构
     */
    @Override
    public Integer insertByBo(FolderInfoBo bo) {
        if(bo.getParentFolder() > 0){
            //check if this folder is existed
            boolean existed = hasFolderExisted(bo.getProjectId(), bo.getParentFolder());
            if( !existed){
                throw new ServiceException(String.format("Parent folder('id:%d') is not existed",bo.getParentFolder()));
            }
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int curLoginUserId = loginUser != null ? loginUser.getExternalUserId():0;
        var newFolderId = tableSequenceManager.getNextSequence(SequenceTable.Folder_Info,bo.getProjectId());
        bo.setFolderId(newFolderId);
        bo.setCreatedTime(DateUtils.getNowDate());
        bo.setCreatedBy(curLoginUserId);
        var add = MapstructUtils.convert(bo, FolderInfo.class);

        boolean flag = folderInfoMapper.insert(add) > 0;
        if (flag) {
            var nextDisplayOrder = getNextDisplayOrder(bo.getProjectId(), bo.getParentFolder());
            var folderTreeBo = new FolderTreeBo();
            folderTreeBo.setProjectId(bo.getProjectId());
            folderTreeBo.setParentFolder(bo.getParentFolder());
            folderTreeBo.setChildFolder(bo.getFolderId());
            folderTreeBo.setDisplayOrder(nextDisplayOrder);
            var folderTree = MapstructUtils.convert(folderTreeBo, FolderTree.class);
            flag = baseMapper.insert(folderTree) > 0;
        }
        return flag ? bo.getFolderId():0;
    }

    /**
     * 修改文件夹树结构
     */
    @Override
    public Integer updateByBo(FolderInfoBo bo) {
        if(bo == null){
            throw new ServiceException("FolderInfoBo is null");
        }
        if(bo.getProjectId() == null || bo.getProjectId() == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if( bo.getFolderId() == null || bo.getFolderId() == 0){
            throw new ServiceException("Folder Id can't be 0");
        }
        if( bo.getId() == null || bo.getId() == 0){
            //get id firstly
            var existedFolderInfo = folderInfoMapper.selectOne(new LambdaQueryWrapper<FolderInfo>()
                .eq(FolderInfo::getProjectId, bo.getProjectId())
                .eq(FolderInfo::getFolderId, bo.getFolderId()));
            if(existedFolderInfo == null)
                throw new ServiceException(String.format("Folder(%d-%d) can't not found",bo.getProjectId(),bo.getFolderId()));
            bo.setId(existedFolderInfo.getId());
        }
        FolderInfo update = MapstructUtils.convert(bo, FolderInfo.class);
        return folderInfoMapper.updateById(update) > 0 ? bo.getFolderId() : 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FolderTree entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除文件夹树结构
     */
    @Override
    public Boolean deleteWithValidByIds(List<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithFolderIds(Integer projectId, List<Integer> folderIds) {
        if( folderIds == null || folderIds.isEmpty())
            return false;
        //check if they have children
        var childFolderIds = getChildFoldersWithParentIds(projectId, folderIds);
        if( childFolderIds != null && !childFolderIds.isEmpty()){
            throw new ServiceException("有些文件夹下有子文件夹,不能删除！");
        }
        var lqw = Wrappers.<FolderTree>lambdaQuery()
            .eq(FolderTree::getProjectId, projectId)
            .in(FolderTree::getChildFolder, folderIds);
       boolean flagTree = baseMapper.delete(lqw) > 0;
       var infoQw = Wrappers.<FolderInfo>lambdaQuery()
            .eq(FolderInfo::getProjectId, projectId)
            .in(FolderInfo::getFolderId, folderIds);
        boolean flagInfo = folderInfoMapper.delete(infoQw) > 0;
        return flagTree && flagInfo;
    }

    /**
     * 根据项目ID获取文件夹树结构
     */
    @Override
    public List<Tree<Integer>> getFolderTreeByProjectId(Integer projectId) {
        return getFolderTreeByProjectId(projectId, null);
    }

    /**
     * 根据项目ID和根文件夹ID获取文件夹树结构
     */
    @Override
    public List<Tree<Integer>> getFolderTreeByProjectId(Integer projectId, Integer rootId) {
        // 使用SQL查询获取树形结构数据
        List<FolderTreeNodeVo> treeNodes = baseMapper.selectFolderTreeByProjectIdAndRootId(projectId, rootId);

        if (CollUtil.isEmpty(treeNodes)) {
            return CollUtil.newArrayList();
        }

        // 构建树形结构
        TreeNodeConfig config = new TreeNodeConfig();
        config.setIdKey("folderId");
        config.setParentIdKey("parentId");
        config.setNameKey("folderName");
        config.setWeightKey("displayOrder");
        config.setChildrenKey("children");

        return TreeUtil.build(treeNodes, 0, config, (node, tree) -> {
            tree.setId(node.getFolderId());
            tree.setParentId(node.getParentId());
            tree.setName(node.getFolderName());
            tree.setWeight(node.getDisplayOrder());
        });
    }

    /**
     * 根据项目ID和父文件夹ID获取子文件夹列表
     */
    @Override
    public List<FolderTreeVo> getChildFoldersByParentId(Integer projectId, Integer parentFolder) {
        // 使用关联查询获取带有文件夹名称的数据
        return baseMapper.selectChildFoldersWithNames(projectId, parentFolder);
    }
    /**
     * 获取包含报告的文件夹ID集合
     */
    protected Set<Integer> getChildFoldersWithParentIds(Integer projectId,List<Integer> parentFolderIds) {
        LambdaQueryWrapper<FolderTree> lqw = Wrappers.lambdaQuery();
        lqw.eq(FolderTree::getProjectId, projectId);
        if (parentFolderIds != null && !parentFolderIds.isEmpty()) {
            lqw.in(FolderTree::getParentFolder, parentFolderIds);
        }
        lqw.select(FolderTree::getChildFolder);
        return baseMapper.selectList(lqw).stream()
            .map(FolderTree::getChildFolder)
            .collect(Collectors.toSet());
    }
    protected boolean hasFolderExisted(Integer projectId, Integer folderId) {
        LambdaQueryWrapper<FolderInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(FolderInfo::getProjectId, projectId);
        lqw.eq(FolderInfo::getFolderId, folderId);
        return folderInfoMapper.exists(lqw);
    }
    protected Integer getNextDisplayOrder(Integer projectId, Integer parentFolder) {
        LambdaQueryWrapper<FolderTree> lqw = Wrappers.lambdaQuery();
        lqw.eq(FolderTree::getProjectId, projectId);
        lqw.eq(FolderTree::getParentFolder, parentFolder);
        lqw.orderByDesc(FolderTree::getDisplayOrder);
        lqw.last("limit 1");
        lqw.select(FolderTree::getDisplayOrder);
        var folderTreeDisplayOrder = baseMapper.selectObjs(lqw);
        return (folderTreeDisplayOrder == null || folderTreeDisplayOrder.isEmpty()) ? 1 : ValueConvert.readInt(folderTreeDisplayOrder.get(0)) + 1;
    }
}
