package org.dromara.servicetrack.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.vo.ListViewFieldVo;
import org.dromara.servicetrack.domain.bo.CustomerInfoBo;
import org.dromara.servicetrack.domain.bo.CustomerInfoListBo;
import org.dromara.servicetrack.domain.vo.CustomerInfoListVo;
import org.dromara.servicetrack.domain.vo.CustomerInfoVo;

import java.util.Collection;

public interface ICustomerInfoService {
    /**
     * 分页查询User列表
     */
    TableDataInfo<CustomerInfoListVo> selectPageUserList(CustomerInfoListBo bo, PageQuery pageQuery );

    /**
     * 获取User字段详情
     */
    CustomerInfoVo getCustomerInfoDetail(Integer projectId, Integer customerId);
    /**
     * 新增User
     */
    Integer insertByBo(CustomerInfoBo bo);


    /**
     * 修改User info
     */
    Integer updateByBo(CustomerInfoBo bo);

    /**
     * 校验并批量删除User信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获得ListView字段信息
     *  @param projectId 项目ID
     *  @param option 选项：0：可得到字段和选择字段，1：选择字段
     */
    ListViewFieldVo getListviewFields(Integer projectId, Integer option);

    boolean checkCustomerNameUnique(CustomerInfoBo bo);
    boolean checkCustomerEmailUnique(CustomerInfoBo bo);
    boolean checkCustomerPhoneExist(CustomerInfoBo bo);
}
