package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.servicetrack.domain.bo.ItemFieldBo;

import java.util.Date;
/**
 *  Item_DateTime
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_datetime")
public class ItemDateTime extends ItemField {

    /**
     * date time
     */
    @TableField(value = "datetime")
    private Date dateTime;
}
