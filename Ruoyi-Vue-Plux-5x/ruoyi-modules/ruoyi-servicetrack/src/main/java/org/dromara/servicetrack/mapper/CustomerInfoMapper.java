package org.dromara.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.CustomerInfo;
import org.dromara.servicetrack.domain.UserInfo;
import org.dromara.servicetrack.domain.bo.CustomerInfoBo;
import org.dromara.servicetrack.domain.vo.CustomerInfoListVo;
import org.dromara.servicetrack.domain.vo.CustomerInfoVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.servicetrack.domain.vo.UserInfoListVo;

import java.util.List;

/**
 * 客户信息管理 数据层
 *
 * <AUTHOR>
 */
public interface CustomerInfoMapper extends BaseMapperPlus<CustomerInfo, CustomerInfoVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<CustomerInfo> buildWrapper(CustomerInfoBo bo) {
        LambdaQueryWrapper<CustomerInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, CustomerInfo::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, CustomerInfo::getProjectId, bo.getProjectId());
        lqw.eq(bo.getCustomerId() != null, CustomerInfo::getCustomerId, bo.getCustomerId());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), CustomerInfo::getCustomerName, bo.getCustomerName());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerStatus()), CustomerInfo::getCustomerStatus, bo.getCustomerStatus());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerNotes()), CustomerInfo::getCustomerNotes, bo.getCustomerNotes());
        return lqw;
    }

    /**
     * 分页查询用户列表
     */
    Page<CustomerInfoListVo> selectPageUserList(@Param("page") Page<CustomerInfo> page, @Param(Constants.WRAPPER) Wrapper<CustomerInfo> queryWrapper,
                                                @Param("projectId") Integer projectId, @Param("customerSqlSegment") String customerSqlSegment,
                                                @Param("sortFieldId") Integer sortFieldId, @Param("textFieldIds") List<Integer> textFieldIds,
                                                @Param("dateTimeFieldIds") List<Integer> dateTimeFieldIds, @Param("selectionFieldIds") List<Integer> selectionFieldIds);
    /**
     * 根据项目ID和客户ID查询客户信息
     *
     * @param projectId  项目ID
     * @param customerId 客户ID
     * @return 客户信息
     */
    CustomerInfoVo selectByProjectAndCustomer(@Param("projectId") Integer projectId, @Param("customerId") Integer customerId);

    /**
     * 根据项目ID和客户ID查询客户信息
     *
     * @param projectId  项目ID
     * @param customerId 客户ID
     * @return 客户信息
     */
    CustomerInfoVo getCustomerInfoDetail(@Param("projectId") Integer projectId, @Param("customerId") Integer customerId);

    /**
     * 根据项目ID查询所有客户信息
     *
     * @param projectId 项目ID
     * @return 客户信息列表
     */
    List<CustomerInfoVo> selectByProjectId(@Param("projectId") Integer projectId);

    /**
     * 根据客户状态查询客户信息
     *
     * @param projectId      项目ID
     * @param customerStatus 客户状态
     * @return 客户信息列表
     */
    List<CustomerInfoVo> selectByStatus(@Param("projectId") Integer projectId, @Param("customerStatus") String customerStatus);

    /**
     * 根据客户名称模糊查询客户信息
     *
     * @param projectId    项目ID
     * @param customerName 客户名称
     * @return 客户信息列表
     */
    List<CustomerInfoVo> selectByNameLike(@Param("projectId") Integer projectId, @Param("customerName") String customerName);
}
