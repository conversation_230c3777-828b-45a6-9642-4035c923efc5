package org.dromara.servicetrack.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.servicetrack.constant.*;
import org.dromara.common.servicetrack.domain.bo.ProjectSystemFieldBo;
import org.dromara.common.servicetrack.domain.vo.ListViewFieldVo;
import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.logic.fieldvalue.FieldValueHandler;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.service.IProjectSystemFieldService;
import org.dromara.common.servicetrack.utils.ValueConvert;
import org.dromara.servicetrack.domain.*;
import org.dromara.servicetrack.domain.bo.*;
import org.dromara.servicetrack.domain.vo.*;
import org.dromara.servicetrack.logic.BaseLogic;
import org.dromara.servicetrack.logic.ContactInfoLogic;
import org.dromara.servicetrack.logic.CustomerInfoLogic;
import org.dromara.servicetrack.mapper.*;
import org.dromara.servicetrack.service.IContactInfoService;
import org.dromara.servicetrack.service.IProjectSettingService;
import org.dromara.servicetrack.service.IUserSettingService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 *  联系人管理 服务层实现
 *
 * <AUTHOR> Fei
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ContactInfoServiceImpl implements IContactInfoService {
    private final UserInfoMapper userInfoMapper;
    private final STUserInfoMapper stuserInfoMapper;
    private final TableSequenceManager tableSequenceManager;
    private final ContactInfoTextMapper contactInfoTextMapper;
    private final ContactInfoSelectionMapper contactInfoSelectionMapper;
    private final ContactInfoDateTimeMapper contactInfoDateTimeMapper;
    private final ContactInfoChangelogMapper contactInfoChangelogMapper;
    private final ContactInfoChangelogFieldMapper contactInfoChangelogFieldMapper;
    private final ContactInfoChangelogTextMapper contactInfoChangelogTextMapper;
    private final CustomerContactMapper customerContactMapper;
    private final IProjectSettingService projectSettingService;
    private final IProjectSystemFieldService projectSystemFieldService;
    private final IUserSettingService userSettingService;

    @Override
    public TableDataInfo<ContactInfoListVo> selectPageUserList(ContactInfoListBo bo, PageQuery pageQuery) {
        if( bo.getFieldIds() == null || bo.getFieldIds().isEmpty()){
            List<Integer> defaultSystemFieldIds = eUserSystemFieldDef.getSystemFieldIds();
            bo.setFieldIds(defaultSystemFieldIds);
        }
        List<Integer> textFieldIds = new ArrayList<>();
        List<Integer> dateTimeFieldIds = new ArrayList<>();
        List<Integer> selectionFieldIds = new ArrayList<>();
        if( bo.getFieldIds() != null){
            var customFieldIds = bo.getFieldIds().stream().filter(fieldId -> !FieldIdHelper.IsUserInfoSystemField(fieldId)).toList();
            var pageFields = ProjectManager.getInstance(bo.getProjectId()).getProjectPageFields();

            for(var fieldId:customFieldIds){
                var field = pageFields.stream().filter(f -> f.getFieldId().equals(fieldId)).findFirst().orElse(null);
                if( field == null){
                    continue;
                }
                if( field.getFieldType() == eFieldTypeDef.ShortText.getValue()){
                    textFieldIds.add(fieldId);
                }else if( field.getFieldType() == eFieldTypeDef.Date.getValue()){
                    dateTimeFieldIds.add(fieldId);
                }else if( field.getFieldType() == eFieldTypeDef.Dropdown.getValue() ||
                    field.getFieldType() == eFieldTypeDef.MultipleSelection.getValue() ||
                    field.getFieldType() == eFieldTypeDef.CheckBox.getValue() ||
                    field.getFieldType() == eFieldTypeDef.RadioBox.getValue()){
                    selectionFieldIds.add(fieldId);
                }
            }
        }
        var wrapper = this.buildQueryWrapper(bo);
        this.buildWrapperSortField(bo, wrapper);
        Integer projectId = bo.getProjectId();
        Page<ContactInfoListVo> page = userInfoMapper.selectPageUserList4Contact(pageQuery.build(), wrapper, projectId,bo.getCustomerId(), bo.getSqlSegment(), bo.getSortFieldId(),
                                                                 textFieldIds, dateTimeFieldIds, selectionFieldIds);

        List<Integer> systemFieldIds = bo.getFieldIds() != null ? bo.getFieldIds().stream().filter(eContactSystemFieldDef::IsSystemField).toList() : new ArrayList<>();
        List<Integer> customFieldIds = bo.getFieldIds() == null ? Collections.emptyList() : bo.getFieldIds().stream().filter(fieldId -> !FieldIdHelper.IsContactInfoSystemField(fieldId)).toList();
        for (var userInfoVo : page.getRecords()){
            List<ListFieldVo> fields = new ArrayList<>();
            getSystemFieldValues(userInfoVo, fields, systemFieldIds);
            getCustomFieldValues(userInfoVo, projectId,fields, customFieldIds);
            userInfoVo.setValues(fields);
        }
        return TableDataInfo.build(page);
    }

    @Override
    public ContactInfoVo getContactInfoDetail(Integer projectId, Integer contactId) {
        if(projectId == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if(contactId == 0){
            throw new ServiceException("Contact Id can't be 0");
        }
        ContactInfoVo detail = userInfoMapper.getUserInfoDetail4Contact(projectId, contactId);
        if(detail == null){
            throw  new ServiceException(String.format("User(%d-%d) can't not found",projectId,contactId));
        }
        try{
            detail.parseCustomFields();
        }
        catch (Exception e) {
            log.error("Error parsing custom fields for user {}: {}", contactId, e.getMessage());
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int curLoginUserId = loginUser != null ? loginUser.getExternalUserId():0;
        if( detail.getProjectId() == null || detail.getProjectId() == 0)
            detail.setProjectId(projectId);
        ContactInfoLogic contactInfoLogic = new ContactInfoLogic(tableSequenceManager, curLoginUserId, detail);
        contactInfoLogic.retrieveUserInfoFieldValues();

        //get change logs
//        List<UserInfoChangelogVo> changelogs = userInfoChangelogMapper.selectVoList(new LambdaQueryWrapper<>(UserInfoChangelog.class)
//            .eq(UserInfoChangelog::getProjectId, projectId)
//            .eq(UserInfoChangelog::getUserId,userId)
//            .orderByDesc(UserInfoChangelog::getLogTime));
//
//        if( changelogs != null){
//            for (UserInfoChangelogVo changelog : changelogs) {
//                changelog.parseDescription4MultiLang(eCultureCode.ZH_CN);
//                changelog.setModifiedByName(ProjectManager.getInstance(stConstant.System_Project_Id).getSysUserName(changelog.getModifiedById()));
//            }
//            detail.setChangelogs(changelogs);
//        }

        return detail;
    }
    @Override
    public boolean checkUserNameUnique(ContactInfoBo user) {
        if(user.getUserName() == null || user.getUserName().isEmpty()){
            //check the fields has this field
            if( user.getFields() == null || user.getFields().isEmpty())
                return false;
            var userNameField = user.getFields().stream().filter(f -> f.getFieldId() == eContactSystemFieldDef.UserName.getValue()).findFirst().orElse(null);
            if( userNameField == null)
                return false;
            user.setUserName(ValueConvert.readString(userNameField.getValue()));
        }
        boolean exist = userInfoMapper.exists(new LambdaQueryWrapper<UserInfo>()
            .eq(UserInfo::getUserName, user.getUserName())
            .ne(ObjectUtil.isNotNull(user.getUserId()), UserInfo::getUserId, user.getUserId()));
        if( exist){
            //get user name field name
            var userNameFieldName = ProjectManager.getInstance(user.getProjectId()).getFieldName(eContactSystemFieldDef.UserName.getValue());
            user.setSpecialFieldName(userNameFieldName);
        }
        return !exist;
    }

    @Override
    public boolean checkUserEmailUnique(ContactInfoBo user) {
        if(user.getEmail() == null || user.getEmail().isEmpty()){
            //check the fields has this field
            if( user.getFields() == null || user.getFields().isEmpty())
                return false;
            var emailField = user.getFields().stream().filter(f -> f.getFieldId() == eContactSystemFieldDef.Email.getValue()).findFirst().orElse(null);
            if( emailField == null)
                return false;
            user.setEmail(ValueConvert.readString(emailField.getValue()));
        }
        boolean exist = userInfoMapper.exists(new LambdaQueryWrapper<UserInfo>()
            .eq(UserInfo::getEmail, user.getEmail())
            .ne(ObjectUtil.isNotNull(user.getUserId()), UserInfo::getUserId, user.getUserId()));
        return !exist;
    }

    @Override
    public boolean checkUserPhoneUnique(ContactInfoBo user) {
        if(user.getPhoneNumber() == null || user.getPhoneNumber().isEmpty()){
            //check the fields has this field
            if( user.getFields() == null || user.getFields().isEmpty())
                return false;
            var phoneField = user.getFields().stream().filter(f -> f.getFieldId() == eContactSystemFieldDef.Phone.getValue()).findFirst().orElse(null);
            if( phoneField == null)
                return false;
            user.setPhoneNumber(ValueConvert.readString(phoneField.getValue()));
        }
        boolean exist = userInfoMapper.exists(new LambdaQueryWrapper<UserInfo>()
            .eq(UserInfo::getPhoneNumber, user.getPhoneNumber())
            .ne(ObjectUtil.isNotNull(user.getUserId()), UserInfo::getUserId, user.getUserId()));
        return !exist;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertByBo(ContactInfoBo bo) {
        if(bo.getProjectId() == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if( bo.getFields() == null || bo.getFields().isEmpty()){
            throw new ServiceException("user fields are empty, so can't create user.");
        }
        if( bo.getCustomerId() == null || bo.getCustomerId() == 0){
            throw new ServiceException("linked customer Id can't be 0");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        if( loginUser == null)
            throw new ServiceException("登录用户不存在!");

        int curLoginUserId = loginUser.getExternalUserId();
        bo.setExternalUserId(tableSequenceManager.getNextSequence(SequenceTable.USER));
        bo.setContactId(bo.getExternalUserId());
        bo.setStUserType(eSTUserTypeDef.CWPEnabled.getMask());
        bo.setCreateBy(loginUser.getUserId());
        Date currentTime = DateUtils.getNowDate();
        bo.setCreateTime(currentTime);
        ContactInfoLogic contactInfoLogic = new ContactInfoLogic(tableSequenceManager, curLoginUserId, null);
        UserInfo add = contactInfoLogic.ConvertToContactInfo(bo);
        boolean flag = userInfoMapper.insert(add) > 0;
        //if (flag)
        {
            bo.setUserId(add.getUserId());
            //update contact info other fields
            insertContactInfoOtherFields(bo,false);

            //insert contact info change log
            insertContactInfoChangeLogs(bo);

            //linked contact to customer
            if(bo.getCustomerId() != null && bo.getCustomerId() > 0){
                CustomerContact customerContact = new CustomerContact();
                customerContact.setProjectId(bo.getProjectId());
                customerContact.setContactId(bo.getContactId());
                customerContact.setCustomerId(bo.getCustomerId());
                customerContactMapper.insert(customerContact);
            }
        }
        return flag ? bo.getExternalUserId():0;
    }
    private void insertContactInfoOtherFields(ContactInfoBo bo, boolean clear) {
        if(bo.getTexts() != null && !bo.getTexts().isEmpty()){
            if( clear){
                var fieldIds = bo.getTexts().stream().map(BaseFieldBo::getFieldId).toList();
                contactInfoTextMapper.delete(new LambdaQueryWrapper<ContactInfoText>()
                    .eq(ContactInfoText::getProjectId, bo.getProjectId())
                    .eq(ContactInfoText::getContactId, bo.getContactId())
                    .in(ContactInfoText::getFieldId, fieldIds));
            }
            List<ContactInfoText> list = new ArrayList<ContactInfoText>();;
            for(var text:bo.getTexts()){
                ContactInfoText oneItemText = MapstructUtils.convert(text, ContactInfoText.class);
                list.add(oneItemText);
            }
            contactInfoTextMapper.insertBatch(list);
        }
        if(bo.getSelections() != null && !bo.getSelections().isEmpty()){
            if( clear){
                var fieldIds = bo.getSelections().stream().map(BaseFieldBo::getFieldId).toList();
                contactInfoSelectionMapper.delete(new LambdaQueryWrapper<ContactInfoSelection>()
                    .eq(ContactInfoSelection::getProjectId, bo.getProjectId())
                    .eq(ContactInfoSelection::getContactId, bo.getContactId())
                    .in(ContactInfoSelection::getFieldId, fieldIds));
            }
            List<ContactInfoSelection> list = new ArrayList<ContactInfoSelection>();
            for(var selection:bo.getSelections()){
                ContactInfoSelection oneItemSelection = MapstructUtils.convert(selection, ContactInfoSelection.class);
                list.add(oneItemSelection);
            }
            contactInfoSelectionMapper.insertBatch(list);
        }
        if(bo.getDateTimes() != null && !bo.getDateTimes().isEmpty()){
            if( clear){
                var fieldIds = bo.getDateTimes().stream().map(BaseFieldBo::getFieldId).toList();
                contactInfoDateTimeMapper.delete(new LambdaQueryWrapper<ContactInfoDateTime>()
                    .eq(ContactInfoDateTime::getProjectId, bo.getProjectId())
                    .eq(ContactInfoDateTime::getContactId, bo.getContactId())
                    .in(ContactInfoDateTime::getFieldId, fieldIds));
            }
            List<ContactInfoDateTime> list = new ArrayList<ContactInfoDateTime>();
            for(var dateTime:bo.getDateTimes()){
                ContactInfoDateTime oneItemDateTime = MapstructUtils.convert(dateTime, ContactInfoDateTime.class);
                list.add(oneItemDateTime);
            }
            contactInfoDateTimeMapper.insertBatch(list);
        }
    }
    private  void insertContactInfoChangeLogs(ContactInfoBo bo){
        if(bo.getChangelog() != null){
            ContactInfoChangelog contactInfochangelog = MapstructUtils.convert(bo.getChangelog(), ContactInfoChangelog.class);
            if(contactInfochangelog != null){
                contactInfochangelog.setProjectId(bo.getProjectId());
                contactInfoChangelogMapper.insert(contactInfochangelog);
            }

            if(bo.getChangelog().getChangelogFields() != null && !bo.getChangelog().getChangelogFields().isEmpty()){
                List<ContactInfoChangelogField> list = new ArrayList<ContactInfoChangelogField>();
                for (var changelogField : bo.getChangelog().getChangelogFields()) {
                    ContactInfoChangelogField contactInfochangelogField = MapstructUtils.convert(changelogField, ContactInfoChangelogField.class);
                    if(contactInfochangelogField != null) {
                        contactInfochangelogField.setChangelogId(changelogField.getChangelogId());
                        list.add(contactInfochangelogField);
                    }
                }
                contactInfoChangelogFieldMapper.insertBatch(list);
            }
        }
        if(bo.getChangeTexts() != null && !bo.getChangeTexts().isEmpty()){
            List<ContactInfoChangelogText> list = new ArrayList<ContactInfoChangelogText>();
            for(var changeText:bo.getChangeTexts()){
                ContactInfoChangelogText oneChangeText = MapstructUtils.convert(changeText, ContactInfoChangelogText.class);
                list.add(oneChangeText);
            }
            contactInfoChangelogTextMapper.insertBatch(list);
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateByBo(ContactInfoBo bo) {
        if(bo.getProjectId() == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if(bo.getExternalUserId() == 0 || bo.getContactId() == 0){
            throw new ServiceException("Contact Id can't be 0");
        }
        if( bo.getFields() == null || bo.getFields().isEmpty()){
            throw new ServiceException("User fields are empty, so don't need to update.");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int curLogicUserId = loginUser != null ? loginUser.getExternalUserId():0;
        var contactInfoDetail = getContactInfoDetail(bo.getProjectId(), bo.getContactId());
        if(contactInfoDetail == null){
            throw new ServiceException(String.format("User(%d-%d) can't not found",bo.getProjectId(),bo.getExternalUserId()));
        }
        ContactInfoLogic contactInfoLogic = new ContactInfoLogic(tableSequenceManager, curLogicUserId, contactInfoDetail);
        if( bo.getUserId() == null ||  bo.getUserId() == 0)
            bo.setUserId(contactInfoDetail.getUserId());
        var update = contactInfoLogic.ConvertToContactInfo(bo);
        boolean flag = userInfoMapper.updateById(update) > 0;
        if (flag) {
            //update contact info other fields
            insertContactInfoOtherFields(bo,true);

            //insert contact info change log
            insertContactInfoChangeLogs(bo);
        }
        return flag ? bo.getContactId():0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Integer projectId,Integer customerId, Boolean isValid) {
        if(ids == null || ids.isEmpty())
            return false;
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        var contactInfos = userInfoMapper.selectVoByIds(ids);
        if (contactInfos == null || contactInfos.isEmpty()) {
            return false;
        }
        var contactIds = contactInfos.stream().map(STUserInfoVo::getExternalUserId).toList();

        var flag =  userInfoMapper.deleteByIds(ids) > 0;
        //delete these contact ids from customer
        var flagDeletedCustomerContact =  customerContactMapper.deleteCustomerContacts(projectId, customerId, contactIds) > 0;
        if (flag && flagDeletedCustomerContact) {
            //generate deleted changelog
            LoginUser loginUser = LoginHelper.getLoginUser();
            int userId = loginUser != null ? loginUser.getExternalUserId():0;
            List<ContactInfoChangelog> changelogs = new ArrayList<ContactInfoChangelog>();
            List<ContactInfoChangelogField> changelogFieldslist = new ArrayList<ContactInfoChangelogField>();
            for(var contactId:contactIds){
                ContactInfoBo bo = new ContactInfoBo();
                bo.setProjectId(projectId);
                bo.setContactId(contactId);
                bo.setExternalUserId(contactId);
                ContactInfoLogic contactInfoLogic = new ContactInfoLogic(tableSequenceManager,userId,null);
                contactInfoLogic.generateSubmitOrDeleteChangelog(bo,false);
                if(bo.getChangelog() != null ){
                    ContactInfoChangelog oneItemChangelog = MapstructUtils.convert(bo.getChangelog(), ContactInfoChangelog.class);
                    if(oneItemChangelog != null) {
                        oneItemChangelog.setProjectId(bo.getProjectId());
                        changelogs.add(oneItemChangelog);
                    }

                    if(bo.getChangelog().getChangelogFields() != null && !bo.getChangelog().getChangelogFields().isEmpty()) {

                        for (var changelogField : bo.getChangelog().getChangelogFields()) {
                            ContactInfoChangelogField oneChangelogField = MapstructUtils.convert(changelogField, ContactInfoChangelogField.class);
                            if(oneChangelogField != null) {
                                oneChangelogField.setChangelogId(changelogField.getChangelogId());
                                changelogFieldslist.add(oneChangelogField);
                            }
                        }
                    }
                }
            }
            if(!changelogs.isEmpty()){
                contactInfoChangelogMapper.insertBatch(changelogs);
            }
            if(!changelogFieldslist.isEmpty()){
                contactInfoChangelogFieldMapper.insertBatch(changelogFieldslist);
            }
        }
        return flag;
    }

    @Override
    public ListViewFieldVo getListviewFields(Integer projectId, Integer option) {
        ListViewFieldVo vo = new ListViewFieldVo();
        var loginUser = LoginHelper.getLoginUser();
        if( loginUser == null)
            throw new ServiceException("登录用户不存在!");
        var userId = loginUser.getExternalUserId();
        List<Integer> userSettingIds = new ArrayList<>();
        userSettingIds.add(eUserSetting.ListView_SelectedColumns_Contact.getValue());
        var userSelectedFields = userSettingService.selectUserSettingList(projectId, userId,userSettingIds);
        List<Integer> selectedFieldIds = FieldIdHelper.getDefaultContactListViewFieldIds();

        if( userSelectedFields != null && !userSelectedFields.isEmpty())
        {
            var strSelectedFields = userSelectedFields.get(0).getSettingContent();
            if( strSelectedFields != null && !strSelectedFields.isEmpty())
            {
                selectedFieldIds =  StringUtils.splitTo(strSelectedFields, Convert::toInt);
            }
        }
        ProjectSystemFieldBo bo = new ProjectSystemFieldBo();
        bo.setProjectId(projectId);
        bo.setModuleId(eSTModuleIDDef.ContactInfo.getValue());
        var allFields = projectSystemFieldService.selectFieldList(bo);
        var pageFields = ProjectManager.getInstance(projectId).getProjectPageFields();

        return BaseLogic.getListviewFields(allFields, pageFields, selectedFieldIds, eSTModuleIDDef.ContactInfo, option);
    }

    private void getSystemFieldValues(ContactInfoListVo contactInfoListVo, List<ListFieldVo> fields, List<Integer> systemFieldIds) {
        if(  contactInfoListVo == null || systemFieldIds == null || systemFieldIds.isEmpty() )
            return;

        for(var fieldId: systemFieldIds) {
            switch (eContactSystemFieldDef.from(fieldId)) {
                case UserId: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setChoiceId(contactInfoListVo.getExternalUserId());
                            setValue(contactInfoListVo.getExternalUserId().toString());
                        }
                    });
                    break;
                }
                case UserName: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(contactInfoListVo.getUserName());
                        }
                    });
                    break;
                }
                case NickName: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(contactInfoListVo.getNickName());
                        }
                    });
                    break;
                }
                case Email: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(contactInfoListVo.getEmail());
                        }
                    });
                    break;
                }
                case Phone: {
                    fields.add( new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(contactInfoListVo.getPhoneNumber());
                        }
                    });
                    break;
                }
                case Status: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(contactInfoListVo.getStatus() == 1 ? "1" : "0");
                        }
                    });
                    break;
                }
                case Depart: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(contactInfoListVo.getDeptName());
                        }
                    });
                    break;
                }
                case CreatedTime: {
                    String createdTime = DateUtils.dateTime(contactInfoListVo.getCreateTime());
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(createdTime);
                        }
                    });
                    break;
                }
                case CreatedBy: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setChoiceId(contactInfoListVo.getCreateBy());
                            setValue(ProjectManager.getInstance(stConstant.System_Project_Id).getSysUserNickName(contactInfoListVo.getCreateBy()));
                        }
                    });
                    break;
                }
            }
        }
    }
    private void getCustomFieldValues(ContactInfoListVo contactInfoListVo , Integer projectId, List<ListFieldVo> fields, List<Integer> customFieldIds){
        if(  contactInfoListVo == null || customFieldIds == null || customFieldIds.isEmpty() )
            return;

        try {
            contactInfoListVo.parseCustomFields();

            Map<String, String> textMap = contactInfoListVo.getTextFields();
            if( textMap != null && !textMap.isEmpty()){
                for( var entry : textMap.entrySet()){
                    int fieldId = Integer.parseInt(entry.getKey());
                    fields.add(new ListFieldVo(){
                        {
                            setId(fieldId);
                            setValue(entry.getValue());
                        }
                    });
                }
            }
            var selectionMap = contactInfoListVo.getSelectionFields();
            if( selectionMap != null && !selectionMap.isEmpty()){
                for( var entry : selectionMap.entrySet()){
                    int fieldId = Integer.parseInt(entry.getKey());
                    FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId,eSTModuleIDDef.ContactInfo, fieldId,0);
                    IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
                    oldFieldValue.readValueFromDB(entry.getValue());
                    fields.add(new ListFieldVo(){
                        {
                            setId(fieldId);
                            setChoiceId(oldFieldValue.getRawValue());
                            setValue(oldFieldValue.getDisplayValue());
                        }
                    });
                }
            }
            var dateTimeMap = contactInfoListVo.getDatetimeFields();
            if( dateTimeMap != null && !dateTimeMap.isEmpty()){
                for( var entry : dateTimeMap.entrySet()) {
                    int fieldId = Integer.parseInt(entry.getKey());
                    FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId,eSTModuleIDDef.ContactInfo, fieldId,0);
                    IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
                    oldFieldValue.readValueFromDB(entry.getValue());
                    fields.add(new ListFieldVo(){
                        {
                            setId(fieldId);
                            setValue(oldFieldValue.getDisplayValue());
                        }
                    });
                }
            }
        }
        catch (Exception e) {
            log.error("parseCustomFields in user's getCustomFieldValues error",e);
        }
    }
    private void buildWrapperSortField(UserInfoListBo bo, QueryWrapper<UserInfo> wrapper){
        //sort field
        if(bo.getSortFieldId() != null && bo.getSortFieldId() != 0){
            boolean asc = bo.getSortFieldId() > 0;
            var pageFields = ProjectManager.getInstance(bo.getProjectId()).getProjectPageFields();

            var sortFieldId = Math.abs(bo.getSortFieldId());
            String sortFieldName = "";
            if(FieldIdHelper.IsUserInfoSystemField(sortFieldId)){
                if( sortFieldId == eUserSystemFieldDef.UserId.getValue()){
                    sortFieldName = "u.external_user_id";
                }
                else if( sortFieldId == eUserSystemFieldDef.NickName.getValue()){
                    sortFieldName = "u.nick_name";
                }
                else if( sortFieldId == eUserSystemFieldDef.UserName.getValue()) {
                    sortFieldName = "u.user_name";
                }
                else if( sortFieldId == eUserSystemFieldDef.Status.getValue()) {
                    sortFieldName = "u.status";
                }
                else if( sortFieldId == eUserSystemFieldDef.Email.getValue()) {
                    sortFieldName = "u.email";
                }
                else if( sortFieldId == eUserSystemFieldDef.Phone.getValue()) {
                    sortFieldName = "u.phonenumber";
                }
                else if( sortFieldId == eUserSystemFieldDef.CreatedTime.getValue()) {
                    sortFieldName = "u.create_time";
                }
                else if( sortFieldId == eUserSystemFieldDef.CreatedBy.getValue()) {
                    sortFieldName = "submittedUser.nick_name";
                }
                else if( sortFieldId == eUserSystemFieldDef.Depart.getValue()) {
                    sortFieldName = "d.dept_name";
                }

            }  else if( FieldIdHelper.IsCustomField(sortFieldId)){
                var field = pageFields.stream().filter(f -> f.getFieldId().equals(sortFieldId)).findFirst().orElse(null);
                if( field != null) {
                    if (field.getFieldType() == eFieldTypeDef.ShortText.getValue()) {
                        sortFieldName = "t.min_text";
                        bo.setSortFieldId(stConstant.ListView_SortField_Text);
                    } else if (field.getFieldType() == eFieldTypeDef.Date.getValue()) {
                        sortFieldName = "d.min_datetime";
                        bo.setSortFieldId(stConstant.ListView_SortField_DateTime);
                    } else if (field.getFieldType() == eFieldTypeDef.Dropdown.getValue() ||
                        field.getFieldType() == eFieldTypeDef.MultipleSelection.getValue() ||
                        field.getFieldType() == eFieldTypeDef.CheckBox.getValue() ||
                        field.getFieldType() == eFieldTypeDef.RadioBox.getValue()) {
                        sortFieldName = "fs.choice_name";
                        bo.setSortFieldId(stConstant.ListView_SortField_Selection);
                    }
                }
            }
            if(!sortFieldName.isEmpty())
                wrapper = asc ? wrapper.orderByAsc(sortFieldName) : wrapper.orderByDesc(sortFieldName);
        }
    }
    private QueryWrapper<UserInfo> buildQueryWrapper(UserInfoListBo bo) {
        QueryWrapper<UserInfo> wrapper = Wrappers.query();
        List<Object> paramValues = new ArrayList<>();

        //just service trick user
        wrapper.ge("u.st_user_type", eSTUserTypeDef.CWPEnabled.getMask());
        paramValues.add(eSTUserTypeDef.CWPEnabled.getMask());
        wrapper.eq( "u.del_flag", 0);
        paramValues.add(0);
        if(bo.getKeyword() != null && !bo.getKeyword().isEmpty()){

            var keyword = bo.getKeyword().trim();
            { // Handle nick_name or user_name queries
                String[] names = keyword.split(",");
                Integer index = 0;
                for (String name : names) {
                   final String processedName = name.replaceAll("(\"|“)(.+?)(\"|”)", "$2").trim();
                    if (index > 0) {
                        wrapper.or(w -> w.like("u.user_name", processedName)
                            .or()
                            .like("u.nick_name", processedName));
                    } else {
                        wrapper.and(w -> w.like("u.user_name", processedName)
                            .or()
                            .like("u.nick_name", processedName));
                    }
                    paramValues.add("'%" + name + "%'"); // 记录参数值
                    paramValues.add("'%" + name + "%'"); // 为nick_name添加一个参数值
                    index++;
                }
            }
        }
        // 获取完整 SQL
        String targetSql = wrapper.getTargetSql();

        // 替换占位符
        for (Object value : paramValues) {
            if (value instanceof List) { // 处理数组值
                List<?> listValue = (List<?>) value;
                for (Object listItem : listValue) {
                    targetSql = targetSql.replaceFirst("\\?", listItem.toString());
                }
            } else { // 处理单个值
                targetSql = targetSql.replaceFirst("\\?", value.toString());
            }
        }
        //just get item's sql segment for query, if there are any other table join, please keep just get user level sql segment
        bo.setSqlSegment(targetSql);
        System.out.println("Generated SQL: " + targetSql);
        return wrapper;
    }
}
