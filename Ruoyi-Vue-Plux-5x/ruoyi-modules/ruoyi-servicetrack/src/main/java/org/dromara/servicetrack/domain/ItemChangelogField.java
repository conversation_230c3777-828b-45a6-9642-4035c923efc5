package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *  Item_changelog_field
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_changelog_field")
public class ItemChangelogField extends BaseChangelogField {
    /**
     * item id
     */
    private Integer itemId;
}
