package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ContactInfoDateTime;

import java.util.Date;

/**
 * 联系人信息时间字段视图对象 contact_info_datetime
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ContactInfoDateTime.class)
public class ContactInfoDateTimeVo extends ContactInfoFieldVo{
    /**
     * 日期时间
     */
    private Date dateTime;

    /**
     * 联系人名称（扩展字段，用于显示）
     */
    private String contactName;

}
