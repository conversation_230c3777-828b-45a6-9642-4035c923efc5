package org.dromara.servicetrack.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.servicetrack.domain.bo.ReportInfoBo;
import org.dromara.servicetrack.domain.vo.ReportInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 报告信息管理 服务层
 *
 * <AUTHOR>
 */
public interface IReportInfoService {

    /**
     * 查询报告信息
     *
     * @param id 主键
     * @return 报告信息
     */
    ReportInfoVo queryById(Long id);

    /**
     * 分页查询报告信息列表
     *
     * @param bo 报告信息业务对象
     * @param pageQuery 分页查询对象
     * @return 报告信息分页列表
     */
    TableDataInfo<ReportInfoVo> queryPageList(ReportInfoBo bo, PageQuery pageQuery);

    /**
     * 查询报告信息列表
     *
     * @param bo 报告信息业务对象
     * @return 报告信息列表
     */
    List<ReportInfoVo> queryList(ReportInfoBo bo);

    /**
     * 根据项目ID和父文件夹ID获取报告列表
     *
     * @param projectId 项目ID
     * @param parentFolderId 父文件夹ID
     * @return 报告信息列表
     */
    List<ReportInfoVo> getReportListByProjectIdAndFolderId(Integer projectId, Integer parentFolderId);

    /**
     * 根据项目ID和报告ID获取报告信息
     *
     * @param projectId 项目ID
     * @param reportId 报告ID
     * @return 报告信息
     */
    ReportInfoVo getReportInfo(Integer projectId, Integer reportId);

    /**
     * 根据项目ID和父文件夹ID分页获取报告列表
     *
     * @param projectId 项目ID
     * @param parentFolderId 父文件夹ID
     * @param pageQuery 分页查询对象
     * @return 报告信息分页列表
     */
    TableDataInfo<ReportInfoVo> getReportPageListByProjectIdAndFolderId(Integer projectId, Integer parentFolderId, PageQuery pageQuery);

    /**
     * 新增报告信息
     *
     * @param bo 报告信息业务对象
     * @return 新增结果
     */
    Integer insertByBo(ReportInfoBo bo);

    /**
     * 修改报告信息
     *
     * @param bo 报告信息业务对象
     * @return 修改结果
     */
    Integer updateByBo(ReportInfoBo bo);

    /**
     * 校验并批量删除报告信息
     *
     * @param ids 主键集合
     * @param isValid 是否校验
     * @return 删除结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 删除报告信息
     *
     * @param id 主键
     * @return 删除结果
     */
    Boolean deleteById(Long id);
}
