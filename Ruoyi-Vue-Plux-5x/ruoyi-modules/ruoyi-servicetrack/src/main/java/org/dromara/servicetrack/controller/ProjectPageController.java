package org.dromara.servicetrack.controller;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.servicetrack.domain.bo.*;
import org.dromara.common.servicetrack.domain.vo.ProjectPageActionVo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageFieldMVo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageFieldVo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageVo;
import org.dromara.common.servicetrack.service.IProjectPageActionService;
import org.dromara.common.servicetrack.service.IProjectPageFieldServiceEx;
import org.dromara.common.servicetrack.service.IProjectPageService;
import org.dromara.common.web.core.BaseController;
import org.dromara.servicetrack.domain.vo.ProjectPageInfoVo;
import org.dromara.servicetrack.domain.vo.ProjectPageModuleVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 字段管理
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/projectPage")
public class ProjectPageController extends BaseController {
    private final IProjectPageFieldServiceEx projectPageFieldService;
    private final IProjectPageService projectPageService;

    private final IProjectPageActionService projectPageActionService;

    /**
     * project page module
     */
    @GetMapping("/allPageSetting")
    public R<ProjectPageModuleVo> GetAllPageSetting(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                    @RequestParam(required = false, defaultValue = "0") Integer isMobile) {
        var ProjectPageModuleVo = new ProjectPageModuleVo();
        var bo = new ProjectPageFieldBo();
        bo.setProjectId(projectId);
        boolean isMobileFlag = isMobile != null && isMobile > 0;
        List<ProjectPageFieldVo> pageFields = new ArrayList<>();
        if(isMobileFlag){
            var pageMobileFields  =  projectPageFieldService.selectPageFieldMList(bo,true);
            if(pageMobileFields != null && !pageMobileFields.isEmpty()) {
                pageFields = pageMobileFields.stream()
                    .map(ProjectPageFieldMVo::toPageFieldVo)
                    .toList();
            }
        }
        else {
            pageFields =  projectPageFieldService.selectPageFieldList(bo,true);
        }
        ProjectPageModuleVo.setPageFields(pageFields);
        var pageVos = projectPageService.selectPageList(projectId);
        List<ProjectPageInfoVo> pages = pageVos.stream().map(page -> {
            return new ProjectPageInfoVo(page.getPageId(), page.getPageName(), page.getModuleId());
        }).toList();

        ProjectPageModuleVo.setPages(pages);

        var pageActions = projectPageActionService.selectPageActionModelList(projectId);
        ProjectPageModuleVo.setPageActions(pageActions);

        return R.ok(ProjectPageModuleVo);
    }
    /**
     * 项目页面列表
     */
    @GetMapping("/list")
    public R<List<ProjectPageVo>> list(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId) {
        return  R.ok(projectPageService.selectPageList(projectId));
    }
    /**
     * 新增项目页面
     */
    @Log(title = "项目页面管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Integer> add(@Validated @RequestBody ProjectPageBo bo) {
        return R.ok(projectPageService.insertByBo(bo));
    }

    /**
     * 修改项目页面
     */
    @Log(title = "项目页面管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Integer> edit(@Validated @RequestBody ProjectPageBo bo) {
        return R.ok(projectPageService.updateByBo(bo));
    }

    /**
     * 删除项目页面
     *
     * @param ids 主键串
     */
    @Log(title = "项目页面管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectPageService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 项目页面操作列表
     */
    @GetMapping("/action/list")
    public R<List<ProjectPageActionVo>> getPageActionList(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId) {
        return  R.ok(projectPageActionService.selectPageActionList(projectId));
    }
    /**
     * 新增项目页面操作
     */
    @Log(title = "项目页面操作管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/action")
    public R<Integer> addPageAction(@Validated @RequestBody ProjectPageActionBo bo) {
        return R.ok(projectPageActionService.insertByBo(bo));
    }

    /**
     * 修改项目页面操作
     */
    @Log(title = "项目页面操作管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/action")
    public R<Integer> editPageAction(@Validated @RequestBody ProjectPageActionBo bo) {
        return R.ok(projectPageActionService.updateByBo(bo));
    }

    /**
     * 删除项目页面字段
     *
     * @param ids 主键串
     */
    @Log(title = "项目页面操作管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/action/{ids}")
    public R<Void> removePageAction(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectPageActionService.deleteWithValidByIds(List.of(ids), true));
    }
    /*
    * 排序项目行页面顺序
     */
    @PostMapping("/action/sort")
    public R<Void> sortPageAction(@Validated @RequestBody ProjectPageActionSortBo bo) {
        return toAjax(projectPageActionService.sortPageAction(bo));
    }
    /**
     * 项目页面字段列表
     */
    @GetMapping("/field/list")
    public R<List<ProjectPageFieldVo>> getPageFieldList(
        @NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "PageId不能为空") @RequestParam Integer pageId,
        @RequestParam(required = false, defaultValue = "0") Integer isMobile) {
        boolean isMobileFlag = isMobile != null && isMobile > 0;
        List<ProjectPageFieldVo> pageFields = new ArrayList<>();
        if(isMobileFlag){
            var pageMobileFields  =  projectPageFieldService.selectPageFieldMList(projectId,pageId);
            if(pageMobileFields != null && !pageMobileFields.isEmpty()) {
                pageFields = pageMobileFields.stream()
                    .map(ProjectPageFieldMVo::toPageFieldVo)
                    .toList();
            }
        }
        else {
            pageFields = projectPageFieldService.selectPageFieldList(projectId, pageId);
        }
        return  R.ok(pageFields);
    }
    /**
     * 新增项目页面字段
     */
    @Log(title = "项目页面字段管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/field")
    public R<Void> addPageField(@Validated @RequestBody ProjectPageFieldBo bo) {
        return toAjax(projectPageFieldService.insertByBo(bo));
    }

    /**
     * 修改项目页面字段
     */
    @Log(title = "项目页面字段管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/field")
    public R<Void> editPageField(@Validated @RequestBody ProjectPageFieldBinderBo bo) {
        return toAjax(projectPageFieldService.updateByBo(bo));
    }
    /**
     * 修改项目mobile页面字段
     */
    @Log(title = "项目mobile页面字段管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/field/mobile")
    public R<Void> editPageFieldMobile(@Validated @RequestBody ProjectPageFieldMBinderBo bo) {
        return toAjax(projectPageFieldService.updateMobileFieldByBo(bo));
    }
    /**
     * 移动项目行页面字段
     */
    @Log(title = "项目页面字段管理", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PutMapping("/field/moveRow")
    public R<Void> movePageFieldRow(@Validated @RequestBody ProjectPageFieldMoveRowBo bo) {
        return toAjax(projectPageFieldService.moveRowByBo(bo));
    }

    /**
     * 删除项目页面字段
     *
     * @param ids 主键串
     */
    @Log(title = "项目页面字段管理", businessType = BusinessType.DELETE)
    @DeleteMapping("field/{ids}")
    public R<Void> removePageField(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                   @NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectPageFieldService.deleteWithValidByIds(projectId,List.of(ids), true));
    }
}
