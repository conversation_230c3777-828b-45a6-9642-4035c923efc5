package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 联系人信息文本字段对象 contact_info_text
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("contact_info_text")
public class ContactInfoText extends ContactInfoField{
    /**
     * text
     */
    @TableField(value = "text")
    private String text;
}
