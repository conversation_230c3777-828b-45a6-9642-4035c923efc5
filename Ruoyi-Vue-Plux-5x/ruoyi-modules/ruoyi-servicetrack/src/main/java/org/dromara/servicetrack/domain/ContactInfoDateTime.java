package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 联系人信息时间字段对象 contact_info_datetime
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("contact_info_datetime")
public class ContactInfoDateTime extends ContactInfoField{
    /**
     * date time
     */
    @TableField(value = "datetime")
    private Date dateTime;
}
