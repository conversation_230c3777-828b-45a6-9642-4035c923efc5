package org.dromara.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.CustomerInfoChangelog;
import org.dromara.servicetrack.domain.bo.CustomerInfoChangelogBo;
import org.dromara.servicetrack.domain.vo.CustomerInfoChangelogVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * 客户信息变更日志管理 数据层
 *
 * <AUTHOR>
 */
public interface CustomerInfoChangelogMapper extends BaseMapperPlus<CustomerInfoChangelog, CustomerInfoChangelogVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<CustomerInfoChangelog> buildWrapper(CustomerInfoChangelogBo bo) {
        LambdaQueryWrapper<CustomerInfoChangelog> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, CustomerInfoChangelog::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, CustomerInfoChangelog::getProjectId, bo.getProjectId());
        lqw.eq(bo.getChangelogId() != null, CustomerInfoChangelog::getChangelogId, bo.getChangelogId());
        lqw.eq(bo.getCustomerId() != null, CustomerInfoChangelog::getCustomerId, bo.getCustomerId());
        lqw.eq(bo.getChangedById() != null, CustomerInfoChangelog::getChangedById, bo.getChangedById());
        return lqw;
    }

    /**
     * 根据项目ID和客户ID查询变更日志列表
     *
     * @param projectId  项目ID
     * @param customerId 客户ID
     * @return 变更日志列表
     */
    List<CustomerInfoChangelogVo> selectByProjectAndCustomer(@Param("projectId") Integer projectId, @Param("customerId") Integer customerId);

    /**
     * 根据项目ID查询所有客户变更日志
     *
     * @param projectId 项目ID
     * @return 变更日志列表
     */
    List<CustomerInfoChangelogVo> selectByProjectId(@Param("projectId") Integer projectId);

    /**
     * 根据变更日志ID查询详细信息
     *
     * @param projectId   项目ID
     * @param changelogId 变更日志ID
     * @param customerId  客户ID
     * @return 变更日志详情
     */
    CustomerInfoChangelogVo selectByChangelogId(@Param("projectId") Integer projectId, 
                                               @Param("changelogId") Integer changelogId, 
                                               @Param("customerId") Integer customerId);
}
