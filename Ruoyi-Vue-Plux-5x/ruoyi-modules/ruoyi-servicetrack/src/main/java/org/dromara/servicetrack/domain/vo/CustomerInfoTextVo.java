package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.CustomerInfoText;

/**
 * 视图对象 customer_info_text
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CustomerInfoText.class)
public class CustomerInfoTextVo extends CustomerInfoFieldVo{
    /**
     * 文本内容
     */
    private String text;

    /**
     * 客户名称（扩展字段，用于显示）
     */
    private String customerName;
}
