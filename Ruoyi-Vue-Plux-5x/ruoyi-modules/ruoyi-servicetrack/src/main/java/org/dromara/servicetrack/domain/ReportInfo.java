package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.Date;

/**
 * 报告信息对象 report_info
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("report_info")
public class ReportInfo extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * 项目ID
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * 报告ID
     */
    @TableField(value = "report_id")
    private Integer reportId;

    /**
     * 报告类型
     */
    @TableField(value = "report_type")
    private Integer reportType;

    /**
     * 目标项目ID
     */
    @TableField(value = "target_project_id")
    private Integer targetProjectId;

    /**
     * 文件夹ID
     */
    @TableField(value = "folder_id")
    private Integer folderId;

    /**
     * 报告名称
     */
    @TableField(value = "report_name")
    private String reportName;

    /**
     * 报告描述
     */
    @TableField(value = "report_description")
    private String reportDescription;

    /**
     * 创建日期
     */
    @TableField(value = "created_date")
    private Date createdDate;

    /**
     * 创建者
     */
    @TableField(value = "created_by")
    private Integer createdBy;

    /**
     * 修改日期
     */
    @TableField(value = "modified_date")
    private Date modifiedDate;

    /**
     * 修改者
     */
    @TableField(value = "modified_by")
    private Integer modifiedBy;

    /**
     * 报告设置
     */
    @TableField(value = "report_setting")
    private String reportSetting;
}
