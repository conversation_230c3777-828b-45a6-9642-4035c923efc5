package org.dromara.servicetrack.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.servicetrack.constant.*;
import org.dromara.common.servicetrack.domain.bo.ProjectSystemFieldBo;
import org.dromara.common.servicetrack.domain.vo.ListViewFieldVo;
import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.logic.fieldvalue.FieldValueHandler;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.service.IProjectSystemFieldService;
import org.dromara.common.servicetrack.utils.ValueConvert;
import org.dromara.servicetrack.domain.*;
import org.dromara.servicetrack.domain.bo.*;
import org.dromara.servicetrack.domain.vo.*;
import org.dromara.servicetrack.logic.BaseLogic;
import org.dromara.servicetrack.logic.CustomerInfoLogic;
import org.dromara.servicetrack.logic.ItemLogic;
import org.dromara.servicetrack.mapper.*;
import org.dromara.servicetrack.service.ICustomerInfoService;
import org.dromara.servicetrack.service.IProjectSettingService;
import org.dromara.servicetrack.service.IUserSettingService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 *  用户管理 服务层实现
 *
 * <AUTHOR> Fei
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CustomerInfoServiceImpl implements ICustomerInfoService {
    private final CustomerInfoMapper customerInfoMapper;
    private final ItemInfoMapper itemInfoMapper;
    private final TableSequenceManager tableSequenceManager;
    private final CustomerInfoTextMapper customerInfoTextMapper;
    private final CustomerInfoSelectionMapper customerInfoSelectionMapper;
    private final CustomerInfoDateTimeMapper customerInfoDateTimeMapper;
    private final CustomerInfoChangelogMapper customerInfoChangelogMapper;
    private final CustomerInfoChangelogFieldMapper customerInfoChangelogFieldMapper;
    private final CustomerInfoChangelogTextMapper userInfoChangeTextMapper;
    private final IProjectSettingService projectSettingService;
    private final IProjectSystemFieldService projectSystemFieldService;
    private final IUserSettingService userSettingService;

    @Override
    public TableDataInfo<CustomerInfoListVo> selectPageUserList(CustomerInfoListBo bo, PageQuery pageQuery) {
        if( bo.getFieldIds() == null || bo.getFieldIds().isEmpty()){
            List<Integer> defaultSystemFieldIds = eCustomerSystemFieldDef.getSystemFieldIds();
            bo.setFieldIds(defaultSystemFieldIds);
        }
        List<Integer> textFieldIds = new ArrayList<>();
        List<Integer> dateTimeFieldIds = new ArrayList<>();
        List<Integer> selectionFieldIds = new ArrayList<>();
        if( bo.getFieldIds() != null){
            var customFieldIds = bo.getFieldIds().stream().filter(fieldId -> !FieldIdHelper.IsUserInfoSystemField(fieldId)).toList();
            var pageFields = ProjectManager.getInstance(bo.getProjectId()).getProjectPageFields();

            for(var fieldId:customFieldIds){
                var field = pageFields.stream().filter(f -> f.getFieldId().equals(fieldId)).findFirst().orElse(null);
                if( field == null){
                    continue;
                }
                if( field.getFieldType() == eFieldTypeDef.ShortText.getValue()){
                    textFieldIds.add(fieldId);
                }else if( field.getFieldType() == eFieldTypeDef.Date.getValue()){
                    dateTimeFieldIds.add(fieldId);
                }else if( field.getFieldType() == eFieldTypeDef.Dropdown.getValue() ||
                    field.getFieldType() == eFieldTypeDef.MultipleSelection.getValue() ||
                    field.getFieldType() == eFieldTypeDef.CheckBox.getValue() ||
                    field.getFieldType() == eFieldTypeDef.RadioBox.getValue()){
                    selectionFieldIds.add(fieldId);
                }
            }
        }
        var wrapper = this.buildQueryWrapper(bo);
        this.buildWrapperSortField(bo, wrapper);
        Integer projectId = bo.getProjectId();
        Page<CustomerInfoListVo> page = customerInfoMapper.selectPageUserList(pageQuery.build(), wrapper, projectId,bo.getSqlSegment(), bo.getSortFieldId(),
                                                                 textFieldIds, dateTimeFieldIds, selectionFieldIds);

        List<Integer> systemFieldIds = bo.getFieldIds() != null ? bo.getFieldIds().stream().filter(eCustomerSystemFieldDef::IsSystemField).toList() : new ArrayList<>();
        List<Integer> customFieldIds = bo.getFieldIds() == null ? Collections.emptyList() : bo.getFieldIds().stream().filter(fieldId -> !FieldIdHelper.IsCustomerInfoSystemField(fieldId)).toList();
        for (CustomerInfoListVo customerInfoVo : page.getRecords()){
            List<ListFieldVo> fields = new ArrayList<>();
            getSystemFieldValues(customerInfoVo,projectId, fields, systemFieldIds);
            getCustomFieldValues(customerInfoVo, projectId,fields, customFieldIds);
            customerInfoVo.setValues(fields);
        }
        return TableDataInfo.build(page);
    }

    @Override
    public CustomerInfoVo getCustomerInfoDetail(Integer projectId, Integer customerId) {
        if(projectId == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if(customerId == 0){
            throw new ServiceException("customer Id can't be 0");
        }
        CustomerInfoVo detail = customerInfoMapper.getCustomerInfoDetail(projectId, customerId);
        if(detail == null){
            throw  new ServiceException(String.format("Customer(%d-%d) can't not found",projectId,customerId));
        }
        try{
            detail.parseCustomFields();
        }
        catch (Exception e) {
            log.error("Error parsing custom fields for user {}: {}", customerId, e.getMessage());
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int curLoginUserId = loginUser != null ? loginUser.getExternalUserId():0;
        if( detail.getProjectId() == null || detail.getProjectId() == 0)
            detail.setProjectId(projectId);
        CustomerInfoLogic customerInfoLogic = new CustomerInfoLogic(tableSequenceManager, curLoginUserId, detail);
        customerInfoLogic.retrieveUserInfoFieldValues();

        //get change logs
//        List<UserInfoChangelogVo> changelogs = userInfoChangelogMapper.selectVoList(new LambdaQueryWrapper<>(UserInfoChangelog.class)
//            .eq(UserInfoChangelog::getProjectId, projectId)
//            .eq(UserInfoChangelog::getUserId,userId)
//            .orderByDesc(UserInfoChangelog::getLogTime));
//
//        if( changelogs != null){
//            for (UserInfoChangelogVo changelog : changelogs) {
//                changelog.parseDescription4MultiLang(eCultureCode.ZH_CN);
//                changelog.setModifiedByName(ProjectManager.getInstance(stConstant.System_Project_Id).getSysUserName(changelog.getModifiedById()));
//            }
//            detail.setChangelogs(changelogs);
//        }

        return detail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertByBo(CustomerInfoBo bo) {
        if(bo.getProjectId() == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if( bo.getFields() == null || bo.getFields().isEmpty()){
            throw new ServiceException("user fields are empty, so can't create user.");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int curLoginUserId = loginUser != null ? loginUser.getExternalUserId():0;
        CustomerInfoLogic customerInfoLogic = new CustomerInfoLogic(tableSequenceManager, curLoginUserId, null);
        var add = customerInfoLogic.ConvertToCustomerInfo(bo);
        boolean flag = customerInfoMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            //update customer info other fields
            insertCustomerInfoOtherFields(bo,false);

            //insert customer info change log
            insertCustomerInfoChangeLogs(bo);

            //reset cache
            ProjectManager.getInstance(bo.getProjectId()).resetCustomerList();
        }
        return flag ? bo.getCustomerId():0;
    }
    private void insertCustomerInfoOtherFields(CustomerInfoBo bo, boolean clear) {
        if(bo.getTexts() != null && !bo.getTexts().isEmpty()){
            if( clear){
                var fieldIds = bo.getTexts().stream().map(BaseFieldBo::getFieldId).toList();
                customerInfoTextMapper.delete(new LambdaQueryWrapper<CustomerInfoText>()
                    .eq(CustomerInfoText::getProjectId, bo.getProjectId())
                    .eq(CustomerInfoText::getCustomerId, bo.getCustomerId())
                    .in(CustomerInfoText::getFieldId, fieldIds));
            }
            List<CustomerInfoText> list = new ArrayList<CustomerInfoText>();;
            for(var text:bo.getTexts()){
                CustomerInfoText oneItemText = MapstructUtils.convert(text, CustomerInfoText.class);
                list.add(oneItemText);
            }
            customerInfoTextMapper.insertBatch(list);
        }
        if(bo.getSelections() != null && !bo.getSelections().isEmpty()){
            if( clear){
                var fieldIds = bo.getSelections().stream().map(BaseFieldBo::getFieldId).toList();
                customerInfoSelectionMapper.delete(new LambdaQueryWrapper<CustomerInfoSelection>()
                    .eq(CustomerInfoSelection::getProjectId, bo.getProjectId())
                    .eq(CustomerInfoSelection::getCustomerId, bo.getCustomerId())
                    .in(CustomerInfoSelection::getFieldId, fieldIds));
            }
            List<CustomerInfoSelection> list = new ArrayList<CustomerInfoSelection>();
            for(var selection:bo.getSelections()){
                CustomerInfoSelection oneItemSelection = MapstructUtils.convert(selection, CustomerInfoSelection.class);
                list.add(oneItemSelection);
            }
            customerInfoSelectionMapper.insertBatch(list);
        }
        if(bo.getDateTimes() != null && !bo.getDateTimes().isEmpty()){
            if( clear){
                var fieldIds = bo.getDateTimes().stream().map(BaseFieldBo::getFieldId).toList();
                customerInfoDateTimeMapper.delete(new LambdaQueryWrapper<CustomerInfoDateTime>()
                    .eq(CustomerInfoDateTime::getProjectId, bo.getProjectId())
                    .eq(CustomerInfoDateTime::getCustomerId, bo.getCustomerId())
                    .in(CustomerInfoDateTime::getFieldId, fieldIds));
            }
            List<CustomerInfoDateTime> list = new ArrayList<CustomerInfoDateTime>();
            for(var dateTime:bo.getDateTimes()){
                CustomerInfoDateTime oneItemDateTime = MapstructUtils.convert(dateTime, CustomerInfoDateTime.class);
                list.add(oneItemDateTime);
            }
            customerInfoDateTimeMapper.insertBatch(list);
        }
    }
    private  void insertCustomerInfoChangeLogs(CustomerInfoBo bo){
        if(bo.getChangelog() != null){
            CustomerInfoChangelog customerInfochangelog = MapstructUtils.convert(bo.getChangelog(), CustomerInfoChangelog.class);
            if(customerInfochangelog != null){
                customerInfochangelog.setProjectId(bo.getProjectId());
                customerInfoChangelogMapper.insert(customerInfochangelog);
            }

            if(bo.getChangelog().getChangelogFields() != null && !bo.getChangelog().getChangelogFields().isEmpty()){
                List<CustomerInfoChangelogField> list = new ArrayList<CustomerInfoChangelogField>();
                for (var changelogField : bo.getChangelog().getChangelogFields()) {
                    CustomerInfoChangelogField customerInfochangelogField = MapstructUtils.convert(changelogField, CustomerInfoChangelogField.class);
                    if(customerInfochangelogField != null) {
                        customerInfochangelogField.setChangelogId(changelogField.getChangelogId());
                        list.add(customerInfochangelogField);
                    }
                }
                customerInfoChangelogFieldMapper.insertBatch(list);
            }
        }
        if(bo.getChangeTexts() != null && !bo.getChangeTexts().isEmpty()){
            List<CustomerInfoChangelogText> list = new ArrayList<CustomerInfoChangelogText>();
            for(var changeText:bo.getChangeTexts()){
                CustomerInfoChangelogText oneChangeText = MapstructUtils.convert(changeText, CustomerInfoChangelogText.class);
                list.add(oneChangeText);
            }
            userInfoChangeTextMapper.insertBatch(list);
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateByBo(CustomerInfoBo bo) {
        if(bo.getProjectId() == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if(bo.getCustomerId() == 0){
            throw new ServiceException("customer Id can't be 0");
        }
        if( bo.getFields() == null || bo.getFields().isEmpty()){
            throw new ServiceException("Customer fields are empty, so don't need to update.");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int curLogicUserId = loginUser != null ? loginUser.getExternalUserId():0;
        var customerInfoVo = getCustomerInfoDetail(bo.getProjectId(), bo.getCustomerId());
        if(customerInfoVo == null){
            throw new ServiceException(String.format("Customer(%d-%d) can't not found",bo.getProjectId(),bo.getCustomerId()));
        }
        boolean changedCustomerName = !customerInfoVo.getCustomerName().equals(bo.getCustomerName());
        CustomerInfoLogic customerInfoLogic = new CustomerInfoLogic(tableSequenceManager, curLogicUserId, customerInfoVo);
        if( bo.getId() == null ||  bo.getId() == 0)
            bo.setId(customerInfoVo.getId());
        var update = customerInfoLogic.ConvertToCustomerInfo(bo);
        boolean flag = customerInfoMapper.updateById(update) > 0;
        if (flag) {
            //update user info other fields
            insertCustomerInfoOtherFields(bo,true);

            //insert user info change log
            insertCustomerInfoChangeLogs(bo);

            //reset cache if customer name changed
            if( changedCustomerName){
                //reset cache
                ProjectManager.getInstance(bo.getProjectId()).resetCustomerList();
            }
        }
        return flag ? bo.getCustomerId():0;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids,  Boolean isValid) {

        if( ids == null || ids.isEmpty())
            return false;

        var customerInfos = customerInfoMapper.selectVoByIds(ids);
        if (customerInfos == null || customerInfos.isEmpty()) {
            return false;
        }
        Integer projectId = customerInfos.get(0).getProjectId();
        var customerIds = customerInfos.stream().map(CustomerInfoVo::getCustomerId).toList();
        if (isValid) {
          // 做一些业务上的校验,判断是否允许删除
          var workProjectIds = ProjectManager.getInstance(projectId).getWorkProjectIds();
          var existed =  itemInfoMapper.exists(new LambdaQueryWrapper<ItemInfo>()
                    .in(ItemInfo::getProjectId, workProjectIds)
                 .in(ItemInfo::getCustomerId, customerIds));
          if( existed){
                throw new ServiceException("有些客户正在使用中,不能删除！");
            }
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int userId = loginUser != null ? loginUser.getExternalUserId():0;
        boolean flag = customerInfoMapper.update(null, new LambdaUpdateWrapper<CustomerInfo>()
                    .eq(CustomerInfo::getProjectId, projectId)
                    .in(CustomerInfo::getCustomerId, customerIds)
                    .set(CustomerInfo::getDelFlag, "2")
                    .set(CustomerInfo::getModifiedTime, DateUtils.getNowDate())
                    .set(CustomerInfo::getModifiedBy, userId )) > 0;
        //reset cache
        if( flag) {
            //generate deleted changelog
            List<CustomerInfoChangelog> changelogs = new ArrayList<CustomerInfoChangelog>();
            List<CustomerInfoChangelogField> changelogFieldslist = new ArrayList<CustomerInfoChangelogField>();
            for(var customerId:customerIds){
                CustomerInfoBo bo = new CustomerInfoBo();
                bo.setProjectId(projectId);
                bo.setCustomerId(customerId);
                CustomerInfoLogic customerInfoLogic = new CustomerInfoLogic(tableSequenceManager,userId,null);
                customerInfoLogic.generateSubmitOrDeleteChangelog(bo,false);
                if(bo.getChangelog() != null ){
                    CustomerInfoChangelog oneItemChangelog = MapstructUtils.convert(bo.getChangelog(), CustomerInfoChangelog.class);
                    if(oneItemChangelog != null) {
                        oneItemChangelog.setProjectId(bo.getProjectId());
                        changelogs.add(oneItemChangelog);
                    }

                    if(bo.getChangelog().getChangelogFields() != null && !bo.getChangelog().getChangelogFields().isEmpty()) {

                        for (var changelogField : bo.getChangelog().getChangelogFields()) {
                            CustomerInfoChangelogField oneChangelogField = MapstructUtils.convert(changelogField, CustomerInfoChangelogField.class);
                            if(oneChangelogField != null) {
                                oneChangelogField.setChangelogId(changelogField.getChangelogId());
                                changelogFieldslist.add(oneChangelogField);
                            }
                        }
                    }
                }
            }
            if(!changelogs.isEmpty()){
                customerInfoChangelogMapper.insertBatch(changelogs);
            }
            if(!changelogFieldslist.isEmpty()){
                customerInfoChangelogFieldMapper.insertBatch(changelogFieldslist);
            }
            ProjectManager.getInstance(projectId).resetCustomerList();
        }
        return flag;
    }

    @Override
    public ListViewFieldVo getListviewFields(Integer projectId, Integer option) {
        ListViewFieldVo vo = new ListViewFieldVo();
        var loginUser = LoginHelper.getLoginUser();
        if( loginUser == null)
            throw new ServiceException("登录用户不存在!");
        var userId = loginUser.getExternalUserId();
        List<Integer> userSettingIds = new ArrayList<>();
        userSettingIds.add(eUserSetting.ListView_SelectedColumns_Customer.getValue());
        var userSelectedFields = userSettingService.selectUserSettingList(projectId, userId,userSettingIds);
        List<Integer> selectedFieldIds = FieldIdHelper.getDefaultCustomerListViewFieldIds();

        if( userSelectedFields != null && !userSelectedFields.isEmpty())
        {
            var strSelectedFields = userSelectedFields.get(0).getSettingContent();
            if( strSelectedFields != null && !strSelectedFields.isEmpty())
            {
                selectedFieldIds =  StringUtils.splitTo(strSelectedFields, Convert::toInt);
            }
        }
        ProjectSystemFieldBo bo = new ProjectSystemFieldBo();
        bo.setProjectId(projectId);
        bo.setModuleId(eSTModuleIDDef.CustomerInfo.getValue());
        var allFields = projectSystemFieldService.selectFieldList(bo);
        var pageFields = ProjectManager.getInstance(projectId).getProjectPageFields();

        return BaseLogic.getListviewFields(allFields, pageFields, selectedFieldIds, eSTModuleIDDef.CustomerInfo, option);
    }

    @Override
    public boolean checkCustomerNameUnique(CustomerInfoBo bo) {
        if(bo.getCustomerName() == null || bo.getCustomerName().isEmpty()){
            //check the fields has this field
            if( bo.getFields() == null || bo.getFields().isEmpty())
                return true;
            var customerNameField = bo.getFields().stream().filter(f -> f.getFieldId() == eCustomerSystemFieldDef.CustomerName.getValue()).findFirst().orElse(null);
            if( customerNameField == null)
                return true;
            bo.setCustomerName(ValueConvert.readString(customerNameField.getValue()));
        }
        boolean exist = customerInfoMapper.exists(new LambdaQueryWrapper<CustomerInfo>()
            .eq(CustomerInfo::getCustomerName, bo.getCustomerName())
            .ne(ObjectUtil.isNotNull(bo.getCustomerId()), CustomerInfo::getCustomerId, bo.getCustomerId()));
        if( exist){
            //get customer name field name
            var customerNameFieldName = ProjectManager.getInstance(bo.getProjectId()).getFieldName(eCustomerSystemFieldDef.CustomerName.getValue());
            bo.setSpecialFieldName(customerNameFieldName);
        }

        return !exist;
    }

    @Override
    public boolean checkCustomerEmailUnique(CustomerInfoBo bo) {
        return false;
    }

    @Override
    public boolean checkCustomerPhoneExist(CustomerInfoBo bo) {
        return false;
    }

    private void getSystemFieldValues(CustomerInfoListVo customerInfoVo, Integer projectId, List<ListFieldVo> fields, List<Integer> systemFieldIds) {
        if(  customerInfoVo == null || systemFieldIds == null || systemFieldIds.isEmpty() )
            return;

        for(var fieldId: systemFieldIds) {
            switch (eCustomerSystemFieldDef.from(fieldId)) {
                case CustomerId: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setChoiceId(customerInfoVo.getCustomerId());
                            setValue(customerInfoVo.getCustomerId().toString());
                        }
                    });
                    break;
                }
                case CustomerName: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(customerInfoVo.getCustomerName());
                        }
                    });
                    break;
                }
                case CustomerStatus: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(customerInfoVo.getCustomerStatus() != null ? customerInfoVo.getCustomerStatus() : "");
                        }
                    });
                    break;
                }
                case CreatedTime: {
                    String createdTime = DateUtils.dateTime(customerInfoVo.getCreatedTime());
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(createdTime);
                        }
                    });
                    break;
                }
                case LastModifiedTime: {
                    String modifiedTime = DateUtils.dateTime(customerInfoVo.getModifiedTime());
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(modifiedTime);
                        }
                    });
                    break;
                }
                case CustomerType: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setChoiceId(customerInfoVo.getCustomerType());
                            setValue(ProjectManager.getInstance(projectId).getChoiceName( fieldId, customerInfoVo.getCustomerType()));
                        }
                    });
                    break;
                }
                case CustomerIndustry: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setChoiceId(customerInfoVo.getCustomerIndustry());
                            setValue(ProjectManager.getInstance(projectId).getChoiceName( fieldId, customerInfoVo.getCustomerType()));
                        }
                    });
                    break;
                }
                case CustomerLevel: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setChoiceId(customerInfoVo.getCustomerLevel());
                            setValue(ProjectManager.getInstance(projectId).getChoiceName( fieldId, customerInfoVo.getCustomerType()));
                        }
                    });
                    break;
                }
                case CustomerAddress: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(customerInfoVo.getCustomerAddress());
                        }
                    });
                    break;
                }
                case CreatedBy:
                case ModifiedBy:{
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(ProjectManager.getInstance(projectId)
                                .getProjectMemberNickName( fieldId == eCustomerSystemFieldDef.CreatedBy.getValue() ? customerInfoVo.getCreatedBy():customerInfoVo.getModifiedBy()));
                        }
                    });
                    break;
                }

            }
        }
    }
    private void getCustomFieldValues(CustomerInfoListVo customerInfoListVo , Integer projectId, List<ListFieldVo> fields, List<Integer> customFieldIds){
        if(  customerInfoListVo == null || customFieldIds == null || customFieldIds.isEmpty() )
            return;

        try {
            customerInfoListVo.parseCustomFields();

            Map<String, String> textMap = customerInfoListVo.getTextFields();
            if( textMap != null && !textMap.isEmpty()){
                for( var entry : textMap.entrySet()){
                    int fieldId = Integer.parseInt(entry.getKey());
                    fields.add(new ListFieldVo(){
                        {
                            setId(fieldId);
                            setValue(entry.getValue());
                        }
                    });
                }
            }
            var selectionMap = customerInfoListVo.getSelectionFields();
            if( selectionMap != null && !selectionMap.isEmpty()){
                for( var entry : selectionMap.entrySet()){
                    int fieldId = Integer.parseInt(entry.getKey());
                    FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId,eSTModuleIDDef.CustomerInfo, fieldId,0);
                    IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
                    oldFieldValue.readValueFromDB(entry.getValue());
                    fields.add(new ListFieldVo(){
                        {
                            setId(fieldId);
                            setChoiceId(oldFieldValue.getRawValue());
                            setValue(oldFieldValue.getDisplayValue());
                        }
                    });
                }
            }
            var dateTimeMap = customerInfoListVo.getDatetimeFields();
            if( dateTimeMap != null && !dateTimeMap.isEmpty()){
                for( var entry : dateTimeMap.entrySet()) {
                    int fieldId = Integer.parseInt(entry.getKey());
                    FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId,eSTModuleIDDef.CustomerInfo, fieldId,0);
                    IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
                    oldFieldValue.readValueFromDB(entry.getValue());
                    fields.add(new ListFieldVo(){
                        {
                            setId(fieldId);
                            setValue(oldFieldValue.getDisplayValue());
                        }
                    });
                }
            }
        }
        catch (Exception e) {
            log.error("parseCustomFields in user's getCustomFieldValues error",e);
        }
    }
    private void buildWrapperSortField(CustomerInfoListBo bo, QueryWrapper<CustomerInfo> wrapper){
        //sort field
        if(bo.getSortFieldId() != null && bo.getSortFieldId() != 0){
            boolean asc = bo.getSortFieldId() > 0;
            var pageFields = ProjectManager.getInstance(bo.getProjectId()).getProjectPageFields();

            var sortFieldId = Math.abs(bo.getSortFieldId());
            String sortFieldName = "";
            if(FieldIdHelper.IsCustomerInfoSystemField(sortFieldId)){
                if( sortFieldId == eCustomerSystemFieldDef.CustomerId.getValue()){
                    sortFieldName = "u.customer_id";
                }
                else if( sortFieldId == eCustomerSystemFieldDef.CustomerName.getValue()){
                    sortFieldName = "u.customer_name";
                }
                else if( sortFieldId == eCustomerSystemFieldDef.CustomerStatus.getValue()) {
                    sortFieldName = "u.customer_status";
                }
                else if( sortFieldId == eCustomerSystemFieldDef.CreatedTime.getValue()) {
                    sortFieldName = "u.created_time";
                }
                else if( sortFieldId == eCustomerSystemFieldDef.LastModifiedTime.getValue()) {
                    sortFieldName = "u.modified_time";
                }
//                else if( sortFieldId == eCustomerSystemFieldDef.CustomerType.getValue()) {
//                    sortFieldName = "ct.type_name";
//                }
//                else if( sortFieldId == eCustomerSystemFieldDef.CustomerIndustry.getValue()) {
//                    sortFieldName = "ci.industry_name";
//                }
//                else if( sortFieldId == eCustomerSystemFieldDef.CustomerLevel.getValue()) {
//                    sortFieldName = "cl.level_name";
//                }
                else if( sortFieldId == eCustomerSystemFieldDef.CustomerAddress.getValue()) {
                    sortFieldName = "u.customer_address";
                }

            }  else if( FieldIdHelper.IsCustomField(sortFieldId)){
                var field = pageFields.stream().filter(f -> f.getFieldId().equals(sortFieldId)).findFirst().orElse(null);
                if( field != null) {
                    if (field.getFieldType() == eFieldTypeDef.ShortText.getValue()) {
                        sortFieldName = "t.min_text";
                        bo.setSortFieldId(stConstant.ListView_SortField_Text);
                    } else if (field.getFieldType() == eFieldTypeDef.Date.getValue()) {
                        sortFieldName = "d.min_datetime";
                        bo.setSortFieldId(stConstant.ListView_SortField_DateTime);
                    } else if (field.getFieldType() == eFieldTypeDef.Dropdown.getValue() ||
                        field.getFieldType() == eFieldTypeDef.MultipleSelection.getValue() ||
                        field.getFieldType() == eFieldTypeDef.CheckBox.getValue() ||
                        field.getFieldType() == eFieldTypeDef.RadioBox.getValue()) {
                        sortFieldName = "fs.choice_name";
                        bo.setSortFieldId(stConstant.ListView_SortField_Selection);
                    }
                }
            }
            if(!sortFieldName.isEmpty())
                wrapper = asc ? wrapper.orderByAsc(sortFieldName) : wrapper.orderByDesc(sortFieldName);
        }
    }
    private QueryWrapper<CustomerInfo> buildQueryWrapper(CustomerInfoListBo bo) {
        QueryWrapper<CustomerInfo> wrapper = Wrappers.query();
        List<Object> paramValues = new ArrayList<>();
        // 设置条件并记录参数值
        wrapper.eq(ObjectUtil.isNotNull(bo.getProjectId()), "u.project_id", bo.getProjectId());
        paramValues.add(bo.getProjectId());
        //ignore deleted item
        wrapper.and(w -> w.eq("u.del_flag", "0").or().isNull("u.del_flag"));
        paramValues.add("0");
        //just service trick user
        if(bo.getKeyword() != null && !bo.getKeyword().isEmpty()){

            var keyword = bo.getKeyword().trim();
            { // Handle customer_name queries
                String[] names = keyword.split(",");
                Integer index = 0;
                for (String name : names) {
                   final String processedName = name.replaceAll("(\"|“)(.+?)(\"|”)", "$2").trim();
                    if (index > 0) {
                        wrapper.or(w -> w.like("u.customer_name", processedName) );
                    } else {
                        wrapper.like("u.customer_name", processedName);
                    }
                    paramValues.add("'%" + name + "%'"); // 记录参数值
                    index++;
                }
            }
        }
        // 获取完整 SQL
        String targetSql = wrapper.getTargetSql();

        // 替换占位符
        for (Object value : paramValues) {
            if (value instanceof List) { // 处理数组值
                List<?> listValue = (List<?>) value;
                for (Object listItem : listValue) {
                    targetSql = targetSql.replaceFirst("\\?", listItem.toString());
                }
            } else { // 处理单个值
                targetSql = targetSql.replaceFirst("\\?", value.toString());
            }
        }
        //just get item's sql segment for query, if there are any other table join, please keep just get user level sql segment
        bo.setSqlSegment(targetSql);
        System.out.println("Generated SQL: " + targetSql);
        return wrapper;
    }
}
