package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 *  user_info
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("user_info")
public class STUserInfo extends STBaseEntity {

    @TableId(value = "key_id")
    private Long id;

    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    private Integer userId;

    /**
     * 职位
     */
    @TableField(value = "job_title")
    private Integer jobTitle;

    /**
     * 职位职责
     */
    @TableField(value = "job_duty")
    private Integer jobDuty;

    /**
     * 团队
     */
    @TableField(value = "support_team")
    private Integer supportTeam;

    /**
     * 主负责人
     */
    @TableField(value = "primary_support")
    private Integer primarySupport;

    /**
     * 副负责人
     */
    @TableField(value = "secondary_support")
    private Integer secondarySupport;

    /**
     * 是否VIP
     */
    @TableField(value = "is_vip")
    private Integer isVip;
}
