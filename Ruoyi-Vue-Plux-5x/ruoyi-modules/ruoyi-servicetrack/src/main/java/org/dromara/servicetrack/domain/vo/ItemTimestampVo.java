package org.dromara.servicetrack.domain.vo;

import java.io.Serializable;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemText;
import org.dromara.servicetrack.domain.ItemTimestamp;

/**
 * item_timestamp表的vo类
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper =true)
@AutoMapper(target = ItemTimestamp.class)
public class ItemTimestampVo extends ItemFieldVo {

    /**
     * seq_no
     */
    private Integer seqNo;

    /**
     * created_by
     */
    private Integer createdBy;

    /**
     * created_by_name
     */
    private String createdByName;

    /**
     * created_time
     */
    private String createdTime;

    /**
     * modified_time
     */
    private String modifiedTime;

    /**
     * content
     */
    private String content;
}
