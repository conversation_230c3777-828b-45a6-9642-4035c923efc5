package org.dromara.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ItemChangelogField;
import org.dromara.servicetrack.domain.vo.ItemChangelogFieldVo;

import java.util.List;

/**
 * 条目changelog field 数据层
 *
 * <AUTHOR> fei
 */
public interface ItemChangelogFieldMapper extends BaseMapperPlus<ItemChangelogField, ItemChangelogFieldVo> {

     /*
      * 根据项目id和条目id查询 item changelog field
      */
     List<ItemChangelogFieldVo> selectItemChangelogField(@Param("projectId") Integer projectId, @Param("itemId") Integer itemId);
}
