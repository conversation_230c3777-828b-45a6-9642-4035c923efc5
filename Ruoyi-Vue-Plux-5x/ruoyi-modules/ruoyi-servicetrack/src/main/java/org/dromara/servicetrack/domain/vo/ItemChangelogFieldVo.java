package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemChangelog;
import org.dromara.servicetrack.domain.ItemChangelogField;

/**
 * 视图对象 item_changelog_field
 */
@Data
@EqualsAndHashCode(callSuper =true)
@AutoMapper(target = ItemChangelogField.class)
public class ItemChangelogFieldVo extends BaseChangelogFieldVo{
    /**
     * item id
     */
    private Integer itemId;
}
