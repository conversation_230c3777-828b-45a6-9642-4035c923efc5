package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ContactInfoSelection;

import java.io.Serializable;

/**
 * 联系人信息选择字段视图对象 contact_info_selection
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ContactInfoSelection.class)
public class ContactInfoSelectionVo extends ContactInfoFieldVo {
    /**
     * 选择ID
     */
    private Integer choiceId;

    /**
     * 联系人名称（扩展字段，用于显示）
     */
    private String contactName;

    /**
     * 选择项名称（扩展字段，用于显示）
     */
    private String choiceName;
}
