package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.servicetrack.domain.SystemSetting;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统设置视图对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SystemSetting.class)
public class SystemSettingVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 键ID
     */
    private Long id;

    /**
     * 设置ID（主键）
     */
    private Integer settingId;

    /**
     * 设置名称
     */
    private String settingName;

    /**
     * 设置选项
     */
    private Integer settingOption;

    /**
     * 设置内容
     */
    private String settingContent;
}
