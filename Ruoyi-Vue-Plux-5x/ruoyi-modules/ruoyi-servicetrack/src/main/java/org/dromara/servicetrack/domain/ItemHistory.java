package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.Date;
/**
 *  Item_change_text
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_history")
public class ItemHistory extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * item id
     */
    @TableField(value = "item_id")
    private Integer itemId;

    /**
     * seq_no
     */
    @TableField(value = "seq_no")
    private Integer seqNo;

    /**
     * datetime
     */
    @TableField(value = "datetime")
    private Date dateTime;

    /**
     * user_id
     */
    @TableField(value = "user_id")
    private Integer userId;

    /**
     * state from
     */
    @TableField(value = "state_from")
    private Integer stateFrom;

    /**
     * state to
     */
    @TableField(value = "state_to")
    private Integer stateTo;

    /**
     * owner from
     */
    @TableField(value = "owner_from")
    private Integer ownerFrom;

    /**
     * owner to
     */
    @TableField(value = "owner_to")
    private Integer ownerTo;

    /*
    * transition
     */
    @TableField(value = "transition")
    private Integer transition;
}
