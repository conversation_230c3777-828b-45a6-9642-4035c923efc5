package org.dromara.servicetrack.domain.vo;

import lombok.Data;
import org.dromara.common.servicetrack.domain.vo.ProjectPageActonModelVo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageFieldVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 视图对象 Project page list 基类
 */
@Data
public class ProjectPageModuleVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 项目 Page fields list
     */
    private List<ProjectPageFieldVo> pageFields;

    /**
     * 项目 Page list
     */
    private List<ProjectPageInfoVo> pages;

    /**
     * 项目 Page actions list
     */
    private List<ProjectPageActonModelVo> pageActions;
}
