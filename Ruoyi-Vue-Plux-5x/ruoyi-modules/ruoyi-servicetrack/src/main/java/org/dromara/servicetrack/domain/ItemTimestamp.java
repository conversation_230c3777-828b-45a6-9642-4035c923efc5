package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * item_timestamp表的实体类
 *
 * <AUTHOR> fei
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_timestamp")
public class ItemTimestamp extends  ItemField {

    /**
     * seq_no
     */
    @TableField(value = "seq_no")
    private Integer seqNo;

    /**
     * created_by
     */
    @TableField(value = "created_by")
    private Integer createdBy;

    /**
     * created_time
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * modified_time
     */
    @TableField(value = "modified_time")
    private Date modifiedTime;

    /**
     * content
     */
    @TableField(value = "content")
    private String content;
}
