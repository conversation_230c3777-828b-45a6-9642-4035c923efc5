package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.UserInfoChangelog;


/**
 * 视图对象 user info changelog
 */
@Data
@EqualsAndHashCode(callSuper =true)
@AutoMapper(target = UserInfoChangelog.class)
public class UserInfoChangelogVo extends BaseChangelogVo{
    /**
     * user id
     */
    private Integer userId;

    /**
     * user name
     */
    private String userName;
}
