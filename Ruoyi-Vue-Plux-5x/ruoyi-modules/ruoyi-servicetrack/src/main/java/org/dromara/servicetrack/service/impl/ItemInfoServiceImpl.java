package org.dromara.servicetrack.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.constant.*;
import org.dromara.common.servicetrack.domain.ItemAttachment;
import org.dromara.common.servicetrack.domain.ProjectInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectSystemFieldBo;
import org.dromara.common.servicetrack.domain.vo.*;
import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.logic.fieldvalue.FieldValueHandler;
import org.dromara.common.servicetrack.mapper.ProjectInfoMapper;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.mapper.ItemAttachmentMapper;
import org.dromara.common.servicetrack.service.IProjectSystemFieldService;
import org.dromara.common.servicetrack.utils.ValueConvert;
import org.dromara.common.web.config.ServerConfig;
import org.dromara.servicetrack.domain.*;
import org.dromara.servicetrack.domain.bo.*;
import org.dromara.servicetrack.domain.vo.*;
import org.dromara.servicetrack.logic.AbstractEntityLogic;
import org.dromara.servicetrack.logic.BaseLogic;
import org.dromara.servicetrack.logic.ItemLogic;
import org.dromara.servicetrack.mapper.*;
import org.dromara.servicetrack.model.field.TFieldValueVo;
import org.dromara.servicetrack.service.IItemInfoService;
import org.dromara.servicetrack.service.IProjectSettingService;
import org.dromara.servicetrack.service.IUserSettingService;
import  org.dromara.common.servicetrack.constant.eUserSetting;
import  org.dromara.common.servicetrack.constant.eProjectSetting;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 条目管理 服务层实现
 *
 * <AUTHOR> Fei
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ItemInfoServiceImpl implements IItemInfoService  {
    private final ItemInfoMapper  baseMapper;
    private final ItemTempInfoMapper itemTempInfoMapper;
    private final ItemTempGroupSettingMapper itemTempGroupSettingMapper;
    private final TableSequenceManager tableSequenceManager;
    private final ItemTextMapper itemTextMapper;
    private final ItemDateTimeMapper itemDateTimeMapper;
    private final ItemSelectionMapper itemSelectionMapper;
    private final ItemChangelogMapper itemChangelogMapper;
    private final ItemChangelogFieldMapper itemChangelogFieldMapper;
    private final ItemChangeTextMapper itemChangeTextMapper;
    private final ItemHistoryMapper itemHistoryMapper;
    private final ItemAttachmentMapper itemAttachmentMapper;
    private final ItemAmountMapper itemAmountMapper;
    private final ItemTimestampMapper itemTimestampMapper;
    private final UserInfoMapper  userInfoMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final IProjectSettingService projectSettingService;
    private final IUserSettingService userSettingService;
    private final IProjectSystemFieldService projectSystemFieldService;
    private final ServerConfig serverConfig;
    /**
     * 查询项目
     */
    @Override
    public ItemInfoVo queryById(Long id) {
        var itemInfoVo = baseMapper.selectVoById(id);
        return getItemDetail(itemInfoVo.getProjectId(),itemInfoVo.getItemId(), itemInfoVo.getProjectId(),false);
    }

    /**
     * 分页查询项目列表
     */
    public TableDataInfo<ItemListVo> selectPageItemList(ItemListBo item, PageQuery pageQuery) {

        List<Integer> textFieldIds = new ArrayList<>();
        List<Integer> dateTimeFieldIds = new ArrayList<>();
        List<Integer> selectionFieldIds = new ArrayList<>();
        List<Integer> amountFieldIds = new  ArrayList<>();
        item.setFieldIds(new ArrayList<>(item.getFieldIds()));//changed to new list for add a new field
        if( item.getFieldIds() != null){
            var customFieldIds = item.getFieldIds().stream().filter(fieldId -> !FieldIdHelper.IsSystemField(fieldId) || fieldId == eSystemFieldDef.Description.getValue() ).toList();
            var pageFields = ProjectManager.getInstance(item.getProjectId()).getProjectPageFields();

            for(var fieldId:customFieldIds){
                var field = pageFields.stream().filter(f -> f.getFieldId().equals(fieldId)).findFirst().orElse(null);
                if( field == null){
                    continue;
                }
                if( field.getFieldType() == eFieldTypeDef.ShortText.getValue() || field.getFieldType() == eFieldTypeDef.PlainText.getValue()
                || field.getFieldType() == eFieldTypeDef.RichText.getValue()){
                    textFieldIds.add(fieldId);
                }else if( field.getFieldType() == eFieldTypeDef.Date.getValue()){
                    dateTimeFieldIds.add(fieldId);
                }else if( field.getFieldType() == eFieldTypeDef.Dropdown.getValue() ||
                        field.getFieldType() == eFieldTypeDef.MultipleSelection.getValue() ||
                        field.getFieldType() == eFieldTypeDef.CheckBox.getValue() ||
                        field.getFieldType() == eFieldTypeDef.RadioBox.getValue()){
                    selectionFieldIds.add(fieldId);
                }
                else if( field.getFieldType() == eFieldTypeDef.Amount.getValue()){
                    amountFieldIds.add(fieldId);
                }
            }
        }
        var itemListDisplaySettingBo = this.getItemListDisplaySetting(item.getProjectId());
        if( itemListDisplaySettingBo != null){
            var selectedFields = itemListDisplaySettingBo.getSelectedFieldIds();
            if( selectedFields != null && !selectedFields.isEmpty()){
                //check if selection field is not in selected fields
                for(var fieldId:selectedFields){
                    if( !FieldIdHelper.IsSystemField(fieldId) && !selectionFieldIds.contains(fieldId)){
                        selectionFieldIds.add(fieldId);
                        item.getFieldIds().add(fieldId);
                    }
                }
            }
        }
        var wrapper = this.buildQueryWrapper(item);
        this.buildWrapperSortField(item, wrapper);
        Integer projectId = item.getProjectId();
        Page<ItemListVo> page = baseMapper.selectPageItemList(pageQuery.build(), wrapper,projectId, item.getSqlSegment(), Math.abs(item.getSortFieldId()),textFieldIds,dateTimeFieldIds,selectionFieldIds,amountFieldIds);

        List<Integer> customFieldIds = item.getFieldIds() == null ? Collections.emptyList() : item.getFieldIds().stream().filter(fieldId -> !FieldIdHelper.IsSystemField(fieldId)).toList();
        List<Integer> systemFieldIds = item.getFieldIds() != null ? item.getFieldIds().stream().filter(FieldIdHelper::IsSystemField).toList() : new ArrayList<>();

        for (ItemListVo itemInfoVo : page.getRecords()) {

            List<ListFieldVo> fields = new ArrayList<>();
            getSystemFieldValues(itemInfoVo, projectId, fields, systemFieldIds);
            getCustomFieldValues(itemInfoVo, projectId,fields, customFieldIds);

            //match item list display setting
            setItemListItemDisplaySetting(itemListDisplaySettingBo,itemInfoVo,fields);

            itemInfoVo.setValues(fields);
        }
        return TableDataInfo.build(page);
    }
    private ItemListDisplaySettingBo getItemListDisplaySetting(Integer projectId) {
        var projectSettingIds = new ArrayList<Integer>();
        projectSettingIds.add(eProjectSetting.ListView_ColorDefinition.getValue());
        var itemListDisplaySettingVo = projectSettingService.selectSettingList(projectId,projectSettingIds);
        ItemListDisplaySettingBo itemListDisplaySettingBo = null;
        if( itemListDisplaySettingVo != null && !itemListDisplaySettingVo.isEmpty()){
            var setting = itemListDisplaySettingVo.get(0);
            if( setting != null && setting.getSettingOption() == 1 && !setting.getSettingContent().isEmpty()){
                try{
                    itemListDisplaySettingBo = JsonUtils.parseObject(setting.getSettingContent(), ItemListDisplaySettingBo.class);
                    if( itemListDisplaySettingBo != null){
                        itemListDisplaySettingBo.setSelectedListFields();
                    }
                }catch (Exception e){
                    log.error("Error parsing item list display setting: {}", e.getMessage());
                }

            }
        }
        return itemListDisplaySettingBo;
    }
    private void setItemListItemDisplaySetting(ItemListDisplaySettingBo itemListDisplaySettingBo, ItemListVo itemInfoVo, List<ListFieldVo> fields) {
        //match item list display setting
        if( itemListDisplaySettingBo != null){
            var selectedFields = itemListDisplaySettingBo.getSelectedFieldIds();
            if( selectedFields != null && !selectedFields.isEmpty()){
                var  matchedFields = fields.stream().filter(field -> selectedFields.contains(field.getId())).toList();
                if (!matchedFields.isEmpty()) {
                    for (var combination : itemListDisplaySettingBo.getChoiceCombinations()) {
                        boolean allMatch = matchedFields.stream().allMatch(field ->
                            combination.getChoices().stream()
                                .anyMatch(choice -> choice.getFieldId().equals(field.getId()) && choice.getChoiceId().equals(field.getChoiceId()))
                        );
                        if (allMatch) {
                            itemInfoVo.setTextColor(combination.getColor());
                            itemInfoVo.setTextBold(combination.getTextBold());
                            break;
                        }
                    }
                }
            }
        }
    }
    private void getSystemFieldValues(ItemListVo itemInfoVo, Integer projectId, List<ListFieldVo> fields, List<Integer> systemFieldIds) {
        if(  itemInfoVo == null || systemFieldIds == null || systemFieldIds.isEmpty() )
            return;

        Integer stateId = itemInfoVo.getStateId();
        Integer ownerId = itemInfoVo.getOwnerId();
        Integer createdBy = itemInfoVo.getCreatedBy();
        Integer modifiedBy =  itemInfoVo.getModifiedBy();
        Integer customerId =  itemInfoVo.getCustomerId();
        Integer employeeId =  itemInfoVo.getEmployeeId();
        Integer moduleId = itemInfoVo.getModuleId();
        Integer typeId = itemInfoVo.getTypeId();

        String stateName = ProjectManager.getInstance(projectId).getWorkflowStateName(stateId);
        String ownerName = ProjectManager.getInstance(projectId).getProjectMemberNickName(ownerId);

        for(var fieldId: systemFieldIds) {
            switch (eSystemFieldDef.from(fieldId)) {
                case Title: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(itemInfoVo.getItemTitle());
                        }
                    });
                    break;
                }
                case Status: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setChoiceId(stateId);
                            setValue(stateName);
                        }
                    });
                }
                break;
                case Owner: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setChoiceId(ownerId);
                            setValue(ownerName);
                        }
                    });
                }
                break;
                case Type: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setChoiceId(typeId);
                            setValue(ProjectManager.getInstance(projectId).getItemTypeName(typeId));
                        }
                    });
                }
                break;
                case SubmittedBy: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setChoiceId(createdBy);
                            setValue(ProjectManager.getInstance(projectId).getProjectMemberNickName(createdBy));
                        }
                    });
                }
                break;
                case IncidentID: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(itemInfoVo.getItemId().toString());
                        }
                    });
                }
                break;
                case SubmittedTime: {
                    String createdTime = DateUtils.dateTime(itemInfoVo.getCreatedTime());
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(createdTime);
                        }
                    });
                }
                break;
                case LastModifiedTime: {
                    String modifiedTime = DateUtils.dateTime(itemInfoVo.getModifiedTime());
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(modifiedTime);
                        }
                    });
                }
                break;
                case LastModifiedBy: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(ProjectManager.getInstance(projectId).getProjectMemberNickName(modifiedBy));
                        }
                    });
                }
                break;
                case Employee: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(ProjectManager.getInstance(stConstant.System_Project_Id).getSysUserNickName(employeeId));
                        }
                    });
                }
                case Customer:{
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(ProjectManager.getInstance(projectId).getCustomerName(customerId));
                        }
                    });
                }
                case ClosedTime: {
                    String closedTime = DateUtils.dateTime(itemInfoVo.getClosedTime());
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(closedTime);
                        }
                    });
                }
                break;
                case ClosedBy: {
                    fields.add(new ListFieldVo() {
                        {
                            setId(fieldId);
                            setValue(ProjectManager.getInstance(projectId).getProjectMemberNickName(itemInfoVo.getClosedBy()));
                        }
                    });
                    break;
                }
            }
        }
    }
    private void getCustomFieldValues(ItemListVo itemInfoVo, Integer projectId, List<ListFieldVo> fields, List<Integer> customFieldIds){
        if(  itemInfoVo == null || customFieldIds == null || customFieldIds.isEmpty() )
            return;

        try {
            itemInfoVo.parseCustomFields();

            Map<String, String> textMap = itemInfoVo.getTextFields();
            if( textMap != null && !textMap.isEmpty()){
                for( var entry : textMap.entrySet()){
                    int fieldId = Integer.parseInt(entry.getKey());
                    String fieldValue = entry.getValue();
                    fieldValue = StringUtils.htmlToText(fieldValue);
                    String finalFieldValue = fieldValue;
                    fields.add(new ListFieldVo(){
                        {
                            setId(fieldId);
                            setValue(finalFieldValue);
                        }
                    });
                }
            }
            var selectionMap = itemInfoVo.getSelectionFields();
            if( selectionMap != null && !selectionMap.isEmpty()){
                for( var entry : selectionMap.entrySet()){
                    int fieldId = Integer.parseInt(entry.getKey());
                    FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId,eSTModuleIDDef.Incident, fieldId,0);
                    IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
                    if( oldFieldValue == null)
                        continue;
                    oldFieldValue.readValueFromDB(entry.getValue());
                    fields.add(new ListFieldVo(){
                        {
                            setId(fieldId);
                            setChoiceId(oldFieldValue.getRawValue());
                            setValue(oldFieldValue.getDisplayValue());
                        }
                    });
                }
            }
            var dateTimeMap = itemInfoVo.getDatetimeFields();
            if( dateTimeMap != null && !dateTimeMap.isEmpty()){
                for( var entry : dateTimeMap.entrySet()) {
                    int fieldId = Integer.parseInt(entry.getKey());
                    FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId,eSTModuleIDDef.Incident, fieldId,0);
                    IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
                    if( oldFieldValue == null)
                        continue;
                    oldFieldValue.readValueFromDB(entry.getValue());
                    fields.add(new ListFieldVo(){
                        {
                            setId(fieldId);
                            setValue(oldFieldValue.getDisplayValue());
                        }
                    });
                }
            }
            var amountMap = itemInfoVo.getAmountFields();
            if( amountMap != null && !amountMap.isEmpty()){
                for( var entry : amountMap.entrySet()){
                    int fieldId = Integer.parseInt(entry.getKey());
                    FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId, eSTModuleIDDef.Incident, fieldId,0);
                    IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
                    if( oldFieldValue == null)
                        continue;
                    oldFieldValue.readValueFromDB(entry.getValue());
                    fields.add(new ListFieldVo(){
                        {
                            setId(fieldId);
                            setValue(oldFieldValue.getDisplayValue());
                        }
                    });
                }
            }
        }
        catch (Exception e) {
            log.error("parseCustomFields in item's getCustomFieldValues error",e);
        }
    }

    @Override
    public ItemInfoVo getItemDetail(Integer projectId, Integer itemId, Integer workProjectId) {
        return getItemDetail(projectId, itemId, workProjectId, false);
    }
    public ItemInfoVo getItemDetail(Integer projectId,  Integer itemId, Integer workProjectId, boolean ignoreWorkflowPermission){
        if(projectId == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if(itemId == 0){
            throw new ServiceException("Item Id can't be 0");
        }
        var detail =  baseMapper.getItemDetail(projectId,itemId);
        if(detail == null){
            throw  new ServiceException(String.format("Item(%d-%d) can't not found",projectId,itemId));
        }
        try {
            detail.parseCustomFields();

            //get attachment list
            var attachments = itemAttachmentMapper.selectItemAttachmentList(projectId,itemId);
            if(attachments != null && !attachments.isEmpty()){
                String url = serverConfig.getUrl();
                for (ItemAttachmentVo attachment : attachments) {
                    Integer createdBy = attachment.getCreatedBy();
                    String createdByName = ProjectManager.getInstance(projectId).getProjectMemberNickName(createdBy);

                    attachment.setUrl(url + attachment.getFileName());
                    if( attachment.getThumbnailUrl() != null && !attachment.getThumbnailUrl().isEmpty() && !attachment.getThumbnailUrl().contains("http"))
                        attachment.setThumbnailUrl(url + attachment.getThumbnailUrl());

                    attachment.setCreatedByName(createdByName);
                }
                detail.setAttachments(attachments);
            }

            //get item timestamp list
            List<ItemTimestampVo> itemTimestamps = itemTimestampMapper.selectByPk(projectId, itemId);
            if( itemTimestamps != null && !itemTimestamps.isEmpty()){
                for (ItemTimestampVo itemTimestamp : itemTimestamps) {
                    Integer createdBy = itemTimestamp.getCreatedBy();
                    String createdByName = ProjectManager.getInstance(projectId).getProjectMemberNickName(createdBy);
                    itemTimestamp.setCreatedByName(createdByName);
                }
                detail.setTimestamps(itemTimestamps);
            }

        } catch (Exception e) {
            log.error("Error parsing custom fields for item {}: {}", itemId, e.getMessage());
        }
        //retrieve item field values to Vo objects
        LoginUser loginUser = LoginHelper.getLoginUser();
        int userId = loginUser != null ? loginUser.getExternalUserId():0;
        ItemLogic itemLogic = new ItemLogic(tableSequenceManager,userId,detail);
        boolean isTemplateModel = workProjectId != null && !workProjectId.equals(projectId);
        Integer transitionId = 0;
        if( isTemplateModel) {
            transitionId =  getHistoryLatestTransition(projectId,itemId);
        }
        itemLogic.retrieveItemFieldValues(workProjectId,ignoreWorkflowPermission,transitionId);
        return detail;
    }
    public Integer getHistoryLatestTransition(Integer projectId, Integer itemId) {
        // 1. 构造分页参数：第 1 页，每页 1 条
        Page<ItemHistory> page = new Page<>(1, 1);

        // 2. 构造查询条件：project_id = ? AND item_id = ?
        QueryWrapper<ItemHistory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId)
            .eq("item_id", itemId)
            .orderByDesc("seq_no");  // 按 seq_no 倒序，最新的在前面

        // 3. 执行分页查询
        Page<ItemHistory> resultPage = itemHistoryMapper.selectPage(page, queryWrapper);

        // 4. 取出第一条记录（如果有）
        if (resultPage.getRecords() != null && !resultPage.getRecords().isEmpty()) {
            return resultPage.getRecords().get(0).getTransition();
        }

        // 5. 没有找到返回 null 或抛异常
        return 0;
    }
    @Override
    public ItemInfoVo getItemTemplateDetail(Integer projectId, Integer templateId,Integer needStateAttributes) {
        if(projectId == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if(templateId == 0){
            throw new ServiceException("template Id can't be 0");
        }
        var baseProjectId = ProjectManager.getInstance(projectId).getBaseProjectId();
        if(baseProjectId == 0){
            throw new ServiceException("baseProject Id can't be 0");
        }
        if( needStateAttributes == null)
            needStateAttributes = 0;
        return getItemDetail(baseProjectId,templateId,projectId, needStateAttributes <= 0);
    }

    @Override
    public WorkflowTransitionStateVo getItemNextState(Integer projectId, Integer itemId, Integer stateId) {
        if(projectId == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int userId = loginUser != null ? loginUser.getExternalUserId():0;
        var nextState =  ProjectManager.getInstance(projectId).getWorkflowTransitionNextState(stateId,userId);
        if(nextState == null){
            log.warn(String.format("Next item's state(%d-%d-%d) can't not found",projectId,itemId,stateId));
        }
        return nextState;
    }

    @Override
    public QueryWrapper<ItemInfo> buildQueryWrapper(ItemListBo item) {
        Map<String, Object> params = item.getParams();
        QueryWrapper<ItemInfo> wrapper = Wrappers.query();
        var projectId = item.getProjectId();
        var stateIds = new ArrayList<Integer>();
        if(item.getStateIds() != null && !item.getStateIds().isEmpty()){
            var onlyOneStateId = item.getStateIds().size() == 1 ? item.getStateIds().get(0) : 0;
            if( onlyOneStateId == -1){ //open state
                var openStates = ProjectManager.getInstance(projectId).getWorkflowOpenStates();
                for(var state:openStates){
                    stateIds.add(state.getStateId());
                }

            }
            else if( onlyOneStateId == -2){ //close state
                var closedStates = ProjectManager.getInstance(projectId).getWorkflowClosedStates();
                for(var state:closedStates){
                    stateIds.add(state.getStateId());
                }
            }
            else {
                stateIds = new ArrayList<>(item.getStateIds());
            }
        }

        List<Object> paramValues = new ArrayList<>();
        // 设置条件并记录参数值
        wrapper.eq(ObjectUtil.isNotNull(item.getProjectId()), "u.project_id", item.getProjectId());
        paramValues.add(item.getProjectId());

        //ignore deleted item
        wrapper.and(w -> w.eq("u.del_flag", "0").or().isNull("u.del_flag"));
        paramValues.add("0");

        if (item.getOwnerIds() != null && !item.getOwnerIds().isEmpty()) {
            if( item.getOwnerIds().size() == 1 &&  item.getOwnerIds().contains(0)){
                //do nothing
            }
            else {
                var ownerIds = new ArrayList<>(item.getOwnerIds().stream().filter(id -> id > 0).toList());
                //including groups
                var groupIds = item.getOwnerIds().stream().filter(id -> id < stConstant.Project_Group_Offset ).toList();
                if( !groupIds.isEmpty()){
                    var projectManager = ProjectManager.getInstance(projectId);
                    for(var groupId:groupIds){
                        var groupUserList = projectManager.getProjectGroupUserList((stConstant.Project_Group_Offset - groupId));
                        ownerIds.add(groupId);
                        for(var groupUserId:groupUserList){
                           if( !ownerIds.contains(groupUserId))
                               ownerIds.add(groupUserId);
                        }
                    }
                }

                wrapper.in("u.owner_id", ownerIds);
                paramValues.add(ownerIds);
            }
        }

        if( !stateIds.isEmpty()){
            wrapper.in("u.state_id", stateIds);
            paramValues.add(stateIds);
        }
        if( params.get("beginTime") != null && params.get("endTime") != null) {
            if(item instanceof DistributionReportBo || item instanceof TrendReportBo)
            {
                Integer dateFielldId = eSystemFieldDef.SubmittedTime.getValue();
                if(item instanceof DistributionReportBo)
                    dateFielldId = ((DistributionReportBo)item).getDateFieldId();
                else
                    dateFielldId = ((TrendReportBo)item).getDateFieldId();

                if( dateFielldId != null)
                 {
                    String dateField = dateFielldId == eSystemFieldDef.SubmittedTime.getValue() ? "u.created_time" : "u.closed_time";
                    wrapper.between(dateField, params.get("beginTime"), params.get("endTime"));
                    paramValues.add("\""+params.get("beginTime") + "\""); //转义时间字符串
                    paramValues.add("\""+params.get("endTime")+ "\""); //转义时间字符串
                }
            }
            else {
                wrapper.between("u.created_time", params.get("beginTime"), params.get("endTime"));
                paramValues.add("\""+params.get("beginTime") + "\""); //转义时间字符串
                paramValues.add("\""+params.get("endTime")+ "\""); //转义时间字符串
            }
        }

        if(item.getKeyword() != null && !item.getKeyword().isEmpty()){
            // Handle item_id queries
            var keyword = item.getKeyword().trim();
            if (keyword.matches("\\d+")) { // Single item_id
                int itemId = Integer.parseInt(keyword);
                wrapper.eq("u.item_id", itemId);
                paramValues.add(itemId); // 记录参数值
            } else if (keyword.matches("\\d+(,\\d+)*")) { // Multiple item_ids
                List<Integer> itemIds = Arrays.stream(keyword.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
                wrapper.in("u.item_id", itemIds);
                paramValues.add(itemIds); // 记录参数值
            } else if (keyword.matches("\\d+-\\d+")) { // item_id range
                String[] range = keyword.split("-");
                int start = Integer.parseInt(range[0]);
                int end = Integer.parseInt(range[1]);
                wrapper.between("u.item_id", start, end);
                paramValues.add(start); // 记录参数值
                paramValues.add(end);   // 记录参数值
            } else if (keyword.matches("(<=|>=|<|>)\\d+")) { // Comparison operators
                String operator = keyword.substring(0, 2);
                int value = Integer.parseInt(keyword.substring(2));
                switch (operator) {
                    case "<=":
                        wrapper.le("u.item_id", value);
                        break;
                    case ">=":
                        wrapper.ge("u.item_id", value);
                        break;
                    case "<":
                        wrapper.lt("u.item_id", value);
                        break;
                    case ">":
                        wrapper.gt("u.item_id", value);
                        break;
                }
                paramValues.add(value); // 记录参数值
            } else if (keyword.matches("\\d+\\*|\\*\\d+")) { // Like queries
                String likeValue = keyword.replace("*", "%");
                wrapper.like("u.display_id", likeValue);
                paramValues.add(likeValue); // 记录参数值
            } else { // Handle item_title queries
                String[] titles = keyword.split(",");
                Integer index = 0;
                for (String title : titles) {
                    title = title.replaceAll("(\"|“)(.+?)(\"|”)", "$2").trim();
                    if( index > 0){
                        wrapper.or().like("u.item_title", title);;
                    }
                    else {
                        wrapper.like("u.item_title", title);;
                    }

                    String replaceTitle = "'%" + title + "%'";
                    paramValues.add(replaceTitle); // 记录参数值
                    index++;
                }
            }
        }
        // 获取完整 SQL
        String targetSql = wrapper.getTargetSql();

        // 替换占位符
        for (Object value : paramValues) {
            if (value instanceof List) { // 处理数组值
                List<?> listValue = (List<?>) value;
                for (Object listItem : listValue) {
                    targetSql = targetSql.replaceFirst("\\?", listItem.toString());
                }
            } else { // 处理单个值
                targetSql = targetSql.replaceFirst("\\?", value.toString());
            }
        }
        //just get item's sql segment for query, if there are any other table join, please keep just get item level sql segment
        item.setSqlSegment(targetSql);
        System.out.println("Generated SQL: " + targetSql);
        return wrapper;
    }
    private void buildWrapperSortField(ItemListBo item, QueryWrapper<ItemInfo> wrapper){
        //sort field
        if(item.getSortFieldId() != null && item.getSortFieldId() != 0){
            boolean asc = item.getSortFieldId() > 0;
            var pageFields = ProjectManager.getInstance(item.getProjectId()).getProjectPageFields();

            var sortFieldId = Math.abs(item.getSortFieldId());
            String sortFieldName = "";
            if(FieldIdHelper.IsAllModuleSystemField(sortFieldId)){
                if( sortFieldId == eSystemFieldDef.Title.getValue()){
                    sortFieldName = "u.item_title";
                }
                else if( sortFieldId == eSystemFieldDef.IncidentID.getValue()){
                    sortFieldName = "u.item_id";
                }
                else if( sortFieldId == eSystemFieldDef.Owner.getValue()) {
                    sortFieldName = "ownerUser.nick_name";
                }
                else if( sortFieldId == eSystemFieldDef.Status.getValue()) {
                    sortFieldName = "st.state_name";
                }
                else if( sortFieldId == eSystemFieldDef.Type.getValue()) {
                }
                else if( sortFieldId == eSystemFieldDef.SubmittedTime.getValue()) {
                    sortFieldName = "u.created_time;";
                }
                else if( sortFieldId == eSystemFieldDef.LastModifiedTime.getValue()) {
                    sortFieldName = "u.modified_time";
                }
                else if( sortFieldId == eSystemFieldDef.SubmittedBy.getValue()) {
                    sortFieldName = "submittedUser.nick_name";
                }
                else if( sortFieldId == eSystemFieldDef.LastModifiedBy.getValue()) {
                    sortFieldName = "modifiedUser.nick_name";
                }
                else if( sortFieldId == eSystemFieldDef.Employee.getValue()) {
                    sortFieldName = "employeeUser.nick_name";
                }
                else if( sortFieldId == eSystemFieldDef.Customer.getValue()) {
                    sortFieldName = "customerUser.customer_name";
                }
                else if( sortFieldId == eSystemFieldDef.ClosedBy.getValue()) {
                    sortFieldName = "closedUser.nick_name";
                }
            } else if (FieldIdHelper.IsIndividualSystemField(sortFieldId)) {
                //to do
            }
            else if( FieldIdHelper.IsCustomField(sortFieldId)){
                var field = pageFields.stream().filter(f -> f.getFieldId().equals(sortFieldId)).findFirst().orElse(null);
                if( field != null) {
                    if (field.getFieldType() == eFieldTypeDef.ShortText.getValue()) {
                        sortFieldName = "t.min_text";
                        item.setSortFieldId(stConstant.ListView_SortField_Text);
                    } else if (field.getFieldType() == eFieldTypeDef.Date.getValue()) {
                        sortFieldName = "d.min_datetime";
                        item.setSortFieldId(stConstant.ListView_SortField_DateTime);
                    } else if (field.getFieldType() == eFieldTypeDef.Dropdown.getValue() ||
                        field.getFieldType() == eFieldTypeDef.MultipleSelection.getValue() ||
                        field.getFieldType() == eFieldTypeDef.CheckBox.getValue() ||
                        field.getFieldType() == eFieldTypeDef.RadioBox.getValue()) {
                        sortFieldName = "fs.choice_name";
                        item.setSortFieldId(stConstant.ListView_SortField_Selection);
                    }
                    else if (field.getFieldType() == eFieldTypeDef.Amount.getValue()) {
                        sortFieldName = "a.min_amount";
                        item.setSortFieldId(stConstant.ListView_SortField_Amount);
                    }
                }
            }
            if(!sortFieldName.isEmpty())
                wrapper = asc ? wrapper.orderByAsc(sortFieldName) : wrapper.orderByDesc(sortFieldName);
        }
    }
    /**
     * 查询项目列表
     */
    @Override
    public TableDataInfo<ItemInfoVo> queryPageList(ItemInfoBo bo, PageQuery pageQuery) {
        return TableDataInfo.<ItemInfoVo>build((com.baomidou.mybatisplus.core.metadata.IPage<ItemInfoVo>) baseMapper.selectVoPage(pageQuery.build(), baseMapper.buildWrapper(bo)));
    }

    /**
     * 查询项目列表
     */
    @Override
    public List<ItemInfoVo> queryList(ItemInfoBo bo) {
        return baseMapper.selectVoList(baseMapper.buildWrapper(bo));
    }

    /**
     * 新增项目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertByBo(ItemInfoBo bo) {
        if(bo.getProjectId() == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if( bo.getFields() == null || bo.getFields().isEmpty()){
            throw new ServiceException("Item fields are empty, so can't create item.");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int userId = loginUser != null ? loginUser.getExternalUserId():0;
        ItemLogic itemLogic = new ItemLogic(tableSequenceManager,userId,null);
        ItemInfo add = itemLogic.ConvertToItemInfo(bo);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());

            //update item other fields
            insertItemOtherFields(bo,false, userId);

            //insert item change log
            insertItemChangeLogs(bo);

            // register logic to execute after transaction commit
            boolean enableSendNotification = ProjectManager.getInstance(bo.getProjectId()).isEmailEnabled();
            if (enableSendNotification) {
                TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            // check if sending notification is needed
                            var itemInfo = getItemDetail(bo.getProjectId(), bo.getItemId(), bo.getProjectId(),flag);
                            ItemLogic itemLogic4SendNotification = new ItemLogic(tableSequenceManager, userId, itemInfo);
                            itemLogic4SendNotification.sendNotification(bo.getProjectId(), bo.getItemId(), true);
                        }
                    }
                );
            }
        }
        return flag?bo.getItemId():0;
    }

    /**
     * 获取预分配的ItemId
     */
    @Override
    public Integer getPreAllocatedItemId(Integer projectId) {
        if(projectId != null){
            var itemId = tableSequenceManager.getNextSequence(SequenceTable.Item_Info,projectId);
            if(itemId > 0){
                return itemId;
            }
        }
        return 0;
    }

    private void insertItemOtherFields(ItemInfoBo bo, boolean clear,Integer userId) {
        if(bo.getTexts() != null && !bo.getTexts().isEmpty()){
            if( clear){
                var fieldIds = bo.getTexts().stream().map(BaseFieldBo::getFieldId).toList();
                itemTextMapper.delete(new LambdaQueryWrapper<ItemText>()
                                         .eq(ItemText::getProjectId, bo.getProjectId())
                                         .eq(ItemText::getItemId, bo.getItemId())
                                         .in(ItemText::getFieldId, fieldIds));
            }
            List<ItemText> list = new ArrayList<ItemText>();;
            for(var text:bo.getTexts()){
                ItemText oneItemText = MapstructUtils.convert(text, ItemText.class);
                list.add(oneItemText);
            }
            itemTextMapper.insertBatch(list);
        }
        if(bo.getSelections() != null && !bo.getSelections().isEmpty()){
            if( clear){
                var fieldIds = bo.getSelections().stream().map(BaseFieldBo::getFieldId).toList();
                itemSelectionMapper.delete(new LambdaQueryWrapper<ItemSelection>()
                                         .eq(ItemSelection::getProjectId, bo.getProjectId())
                                         .eq(ItemSelection::getItemId, bo.getItemId())
                                         .in(ItemSelection::getFieldId, fieldIds));
            }
            List<ItemSelection> list = new ArrayList<ItemSelection>();
            for(var selection:bo.getSelections()){
                ItemSelection oneItemSelection = MapstructUtils.convert(selection, ItemSelection.class);
                list.add(oneItemSelection);
            }
            itemSelectionMapper.insertBatch(list);
        }
        if(bo.getDateTimes() != null && !bo.getDateTimes().isEmpty()){
            if( clear){
                var fieldIds = bo.getDateTimes().stream().map(BaseFieldBo::getFieldId).toList();
                itemDateTimeMapper.delete(new LambdaQueryWrapper<ItemDateTime>()
                                         .eq(ItemDateTime::getProjectId, bo.getProjectId())
                                         .eq(ItemDateTime::getItemId, bo.getItemId())
                                         .in(ItemDateTime::getFieldId, fieldIds));
            }
            List<ItemDateTime> list = new ArrayList<ItemDateTime>();
            for(var dateTime:bo.getDateTimes()){
                ItemDateTime oneItemDateTime = MapstructUtils.convert(dateTime, ItemDateTime.class);
                list.add(oneItemDateTime);
            }
            itemDateTimeMapper.insertBatch(list);
        }
        if(bo.getAmounts() != null && !bo.getAmounts().isEmpty()){
            if( clear){
                var fieldIds = bo.getAmounts().stream().map(BaseFieldBo::getFieldId).toList();
                itemAmountMapper.delete(new LambdaQueryWrapper<ItemAmount>()
                    .eq(ItemAmount::getProjectId, bo.getProjectId())
                    .eq(ItemAmount::getItemId, bo.getItemId())
                    .in(ItemAmount::getFieldId, fieldIds));
            }
            List<ItemAmount> list = new ArrayList<ItemAmount>();
            for(var amount:bo.getAmounts()){
                var oneItemAmount = MapstructUtils.convert(amount, ItemAmount.class);
                list.add(oneItemAmount);
            }
            itemAmountMapper.insertBatch(list);
        }

        if(bo.getTimestamps() != null && !bo.getTimestamps().isEmpty()){
            List<ItemTimestamp> list = new ArrayList<ItemTimestamp>();
            Map<Integer,Integer> timestampSeqNoMap = new HashMap<Integer,Integer>();
            for(var  timestampBo:bo.getTimestamps()){
                var oneTimestampBo = (ItemTimestampBo) timestampBo;
                if( !timestampSeqNoMap.containsKey(oneTimestampBo.getFieldId())){
                    var seqNo = !clear ? 1 : ValueConvert.readInt(itemTimestampMapper.getMaxSeqNo(bo.getProjectId(), bo.getItemId(), oneTimestampBo.getFieldId())) + 1;
                    timestampSeqNoMap.put(oneTimestampBo.getFieldId(),seqNo);
                }
                oneTimestampBo.setSeqNo(timestampSeqNoMap.get(oneTimestampBo.getFieldId()));
                Date currentTime = DateUtils.getNowDate();
                //if(!clear) //to do
                {
                    oneTimestampBo.setCreatedBy(userId);
                    oneTimestampBo.setCreatedTime(currentTime);
                }
                oneTimestampBo.setModifiedTime(currentTime);
                var oneItemTimestamp = MapstructUtils.convert(oneTimestampBo, ItemTimestamp.class);
                list.add(oneItemTimestamp);
            }
            itemTimestampMapper.insertBatch(list);
            //to do
//            if(!clear){
//                itemTimestampMapper.insertBatch(list);
//            }
//            else{
//                itemTimestampMapper.updateBatchById(list);
//            }

        }
    }
    private  void insertItemChangeLogs(ItemInfoBo bo){
        if(bo.getChangelog() != null ){
            ItemChangelog oneItemChangelog = MapstructUtils.convert(bo.getChangelog(), ItemChangelog.class);
            if(oneItemChangelog != null) {
                oneItemChangelog.setProjectId(bo.getProjectId());
                itemChangelogMapper.insert(oneItemChangelog);
            }

            if(bo.getChangelog().getChangelogFields() != null && !bo.getChangelog().getChangelogFields().isEmpty()) {
                List<ItemChangelogField> list = new ArrayList<ItemChangelogField>();
                for (var changelogField : bo.getChangelog().getChangelogFields()) {
                    ItemChangelogField itemchangelogField = MapstructUtils.convert(changelogField, ItemChangelogField.class);
                    if(itemchangelogField != null) {
                        itemchangelogField.setChangelogId(changelogField.getChangelogId());
                        list.add(itemchangelogField);
                    }
                }
                itemChangelogFieldMapper.insertBatch(list);
            }
        }
        if(bo.getChangeTexts() != null && !bo.getChangeTexts().isEmpty()){
            List<ItemChangeText> list = new ArrayList<ItemChangeText>();
            for(var changeText:bo.getChangeTexts()){
                ItemChangeText oneItemchangeText = MapstructUtils.convert(changeText, ItemChangeText.class);
                list.add(oneItemchangeText);
            }
            itemChangeTextMapper.insertBatch(list);
        }
        if(bo.getHistoryBo() != null ){
            ItemHistory oneItemHistory = MapstructUtils.convert(bo.getHistoryBo(), ItemHistory.class);

            itemHistoryMapper.insert(oneItemHistory);
        }
    }
    /**
     * 修改项目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateByBo(ItemInfoBo bo,boolean needSendNotification) {
        if(bo.getProjectId() == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if(bo.getItemId() == 0){
            throw new ServiceException("Item Id can't be 0");
        }
        if( bo.getFields() == null || bo.getFields().isEmpty()){
            throw new ServiceException("Item fields are empty, so don't need to update.");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int userId = loginUser != null ? loginUser.getExternalUserId():0;
        //get item info vo firstly
        var itemInfoVo = getItemDetail(bo.getProjectId(),bo.getItemId(),bo.getProjectId(),true);
        if( itemInfoVo == null ){
            throw new ServiceException(String.format("Item(%d-%d) can't not found",bo.getProjectId(),bo.getItemId()));
        }
        ItemLogic itemLogic = new ItemLogic(tableSequenceManager,userId,itemInfoVo);
        //need to set id
        bo.setId(itemInfoVo.getId());
        if( bo.getItemTitle() == null || bo.getItemTitle().isEmpty()){
            bo.setItemTitle(itemInfoVo.getItemTitle());
        }

        ItemInfo update = itemLogic.ConvertToItemInfo(bo);
        boolean success =  baseMapper.updateById(update) > 0;
        if (success) {

            //update item other fields
            insertItemOtherFields(bo,true,userId);

            //insert item change log
            insertItemChangeLogs(bo);

            // register logic to execute after transaction commit
            boolean enableSendNotification = ProjectManager.getInstance(bo.getProjectId()).isEmailEnabled();
            if(needSendNotification && enableSendNotification) {
                TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            // check if sending notification is needed
                            itemLogic.sendNotification(bo.getProjectId(), bo.getItemId(), false);
                        }
                    }
                );
            }

        }
        return success? bo.getItemId():0;
    }

    /**
     * 批量删除项目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        var items = baseMapper.selectVoByIds(ids);
        if(items == null || items.isEmpty())
            return false;
        var projectId = items.get(0).getProjectId();
        var itemIds = items.stream().map(ItemInfoVo::getItemId).toList();
        LoginUser loginUser = LoginHelper.getLoginUser();
        int userId = loginUser != null ? loginUser.getExternalUserId():0;
        var flag = deleteItems(projectId, userId,itemIds,false);
        if(flag){
            //generate deleted changelog
            List<ItemChangelog> changelogs = new ArrayList<ItemChangelog>();
            List<ItemChangelogField> changelogFieldslist = new ArrayList<ItemChangelogField>();
            for(var itemId:itemIds){
                ItemInfoBo bo = new ItemInfoBo();
                bo.setProjectId(projectId);
                bo.setItemId(itemId);
                ItemLogic itemLogic = new ItemLogic(tableSequenceManager,userId,null);
                itemLogic.generateSubmitOrDeleteChangelog(bo,false);
                if(bo.getChangelog() != null ){
                    ItemChangelog oneItemChangelog = MapstructUtils.convert(bo.getChangelog(), ItemChangelog.class);
                    if(oneItemChangelog != null) {
                        oneItemChangelog.setProjectId(bo.getProjectId());
                        changelogs.add(oneItemChangelog);
                    }

                    if(bo.getChangelog().getChangelogFields() != null && !bo.getChangelog().getChangelogFields().isEmpty()) {

                        for (var changelogField : bo.getChangelog().getChangelogFields()) {
                            ItemChangelogField itemchangelogField = MapstructUtils.convert(changelogField, ItemChangelogField.class);
                            if(itemchangelogField != null) {
                                itemchangelogField.setChangelogId(changelogField.getChangelogId());
                                changelogFieldslist.add(itemchangelogField);
                            }
                        }
                    }
                }
            }
            if(!changelogs.isEmpty()){
                itemChangelogMapper.insertBatch(changelogs);
            }
            if(!changelogFieldslist.isEmpty()){
                itemChangelogFieldMapper.insertBatch(changelogFieldslist);
            }
        }
        return  true;
    }

    /*
    * 插入项目模板
     */
    @Override
    public Integer insertTemplateByBo(ItemTempInfoBo bo) {
        validateTemplateBo(bo,true);
        var itemInfoBo = bo.getItemInfoBo();
        itemInfoBo.setWorkProjectId(bo.getWorkProjectId());
        var templateId = insertByBo(itemInfoBo);
        if( templateId > 0 ){
            bo.setTemplateId(templateId);
            var templateInfo = MapstructUtils.convert(bo, ItemTempInfo.class);
            itemTempInfoMapper.insert(templateInfo);
        }
        return templateId;
    }

    /**
     * 更新项目模板
     */
    @Override
    public Integer updateTemplateByBo(ItemTempInfoBo bo) {
        validateTemplateBo(bo,false);
        var itemInfoBo = bo.getItemInfoBo();
        itemInfoBo.setWorkProjectId(bo.getWorkProjectId());
        var templateId = bo.getItemInfoBo() != null? updateByBo(itemInfoBo, false) : bo.getTemplateId();
        if( templateId > 0 ){
            //get id firstly
            var existedTemplateInfo = itemTempInfoMapper.selectOne(new LambdaQueryWrapper<ItemTempInfo>()
                .eq(ItemTempInfo::getProjectId, bo.getProjectId())
                .eq(ItemTempInfo::getWorkProjectId,bo.getWorkProjectId())
                .eq(ItemTempInfo::getTemplateId, templateId));
            if(existedTemplateInfo == null)
                return  0;
            bo.setId(existedTemplateInfo.getId());
            var templateInfo = MapstructUtils.convert(bo, ItemTempInfo.class);
            itemTempInfoMapper.updateById(templateInfo);
        }
        return templateId;
    }
    private void validateTemplateBo(ItemTempInfoBo bo,boolean needCheckItemInfoBo)
    {
        if(bo.getProjectId() == null || bo.getProjectId() == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if(bo.getWorkProjectId() == null || bo.getWorkProjectId() == 0){
            throw new ServiceException("Work Project Id can't be 0");
        }
        if( bo.getItemInfoBo() == null || bo.getItemInfoBo().getFields() == null || bo.getItemInfoBo().getFields().isEmpty()){
            throw new ServiceException("template fields are empty, so don't need to create template.");
        }
        if(!Objects.equals(bo.getItemInfoBo().getProjectId(), bo.getProjectId())){
            bo.getItemInfoBo().setProjectId(bo.getProjectId());//using base project id
        }
    }

    /**
     * 删除项目模板
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTemplateWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        var templates = itemTempInfoMapper.selectVoByIds(ids);
        if(templates == null || templates.isEmpty())
            return false;
        var projectId = templates.get(0).getProjectId();
        var templateIds = templates.stream().map(ItemTempInfoVo::getTemplateId).toList();

        deleteItems(projectId, 0, templateIds,true);
        return  true;
    }
    private  boolean deleteItems(Integer projectId, Integer userId, List<Integer> itemIds,boolean isTemplate){
        var flag = false;
        if(isTemplate) {
            flag =  itemTempInfoMapper.delete(new QueryWrapper<ItemTempInfo>().eq("project_id", projectId).in("template_id", itemIds)) > 0;
            var projectInfos = projectInfoMapper.selectVoList(new QueryWrapper<ProjectInfo>().eq("baseproject_id", projectId));
            var workProjectIds = projectInfos.stream().map(ProjectInfoVo::getProjectId).toList();
            itemTempGroupSettingMapper.delete(new QueryWrapper<ItemTempGroupSetting>().in("project_id", workProjectIds).in("template_id", itemIds));
        }

        // 使用逻辑删除：通过主键ID删除，会自动设置 del_flag = 2

        if (itemIds != null && !itemIds.isEmpty()) {
           // flag = baseMapper.deleteByIds(keyIds) > 0; //need to update modified time and modified by, can't use deleteByIds
            //need to update del_flag = 2// 这会使用逻辑删除
            flag = baseMapper.update(null, new LambdaUpdateWrapper<ItemInfo>()
                .eq(ItemInfo::getProjectId, projectId)
                .in(ItemInfo::getItemId, itemIds)
                .set(ItemInfo::getDelFlag, "2")
                .set(ItemInfo::getModifiedTime, DateUtils.getNowDate())
                .set(ItemInfo::getModifiedBy, userId)) > 0;
        }

        // 相关表数据仍然物理删除（根据业务需求决定）
        if(isTemplate) {
            itemTextMapper.delete(new QueryWrapper<ItemText>().eq("project_id", projectId).in("item_id", itemIds));
            itemSelectionMapper.delete(new QueryWrapper<ItemSelection>().eq("project_id", projectId).in("item_id", itemIds));
            itemDateTimeMapper.delete(new QueryWrapper<ItemDateTime>().eq("project_id", projectId).in("item_id", itemIds));
            itemHistoryMapper.delete(new QueryWrapper<ItemHistory>().eq("project_id", projectId).in("item_id", itemIds));
            itemChangelogMapper.delete(new QueryWrapper<ItemChangelog>().eq("project_id", projectId).in("item_id", itemIds));
            itemChangelogFieldMapper.delete(new QueryWrapper<ItemChangelogField>().eq("project_id", projectId).in("item_id", itemIds));
            itemChangeTextMapper.delete(new QueryWrapper<ItemChangeText>().eq("project_id", projectId).in("item_id", itemIds));
            itemAttachmentMapper.delete(new QueryWrapper<ItemAttachment>().eq("project_id", projectId).in("item_id", itemIds));
        }
        return flag;
    }
    @Override
    public ListViewFieldVo getListviewFields(Integer projectId,Integer option)
    {
        ListViewFieldVo vo = new ListViewFieldVo();
        var loginUser = LoginHelper.getLoginUser();
        if( loginUser == null)
            throw new ServiceException("登录用户不存在!");
        var userId = loginUser.getExternalUserId();
        List<Integer> userSettingIds = new ArrayList<>();
        userSettingIds.add(eUserSetting.ListView_SelectedColumns.getValue());
        var userSelectedFields = userSettingService.selectUserSettingList(projectId, userId,userSettingIds);
        List<Integer> selectedFieldIds = FieldIdHelper.getDefaultListViewFieldIds();

        if( userSelectedFields != null && !userSelectedFields.isEmpty())
        {
            var strSelectedFields = userSelectedFields.get(0).getSettingContent();
            if( strSelectedFields != null && !strSelectedFields.isEmpty())
            {
                selectedFieldIds =  StringUtils.splitTo(strSelectedFields, Convert::toInt);
            }
        } else{
            //get project setting
            var projectSettingIds = new ArrayList<Integer>();
            projectSettingIds.add(eProjectSetting.ListView_SelectedColumns.getValue());
            var projectSelectedFields =ProjectManager.getInstance(projectId).getProjectSettingList(projectSettingIds);
            if( projectSelectedFields != null && !projectSelectedFields.isEmpty())
            {
                var strSelectedFields = projectSelectedFields.get(0).getSettingContent();
                if( strSelectedFields != null && !strSelectedFields.isEmpty())
                {
                    selectedFieldIds =  StringUtils.splitTo(strSelectedFields, Convert::toInt);
                }
            }
            else{
                //save the default selected fields into project setting
                ProjectSettingBinderBo projectSettingBo = new ProjectSettingBinderBo();
                projectSettingBo.setProjectId(projectId);

                ProjectSettingBinderBo.SettingInfoBo infoBo = new ProjectSettingBinderBo.SettingInfoBo();
                infoBo.setSettingId(eProjectSetting.ListView_SelectedColumns.getValue());
                infoBo.setSettingName("ListView selected Columns");
                infoBo.setSettingContent(StringUtils.join(selectedFieldIds, ","));
                List<ProjectSettingBinderBo.SettingInfoBo> settingList = new ArrayList<>();
                settingList.add(infoBo);
                projectSettingBo.setSettingList(settingList);
                projectSettingService.updateByBo(projectSettingBo);
            }
        }
        ProjectSystemFieldBo bo = new ProjectSystemFieldBo();
        bo.setProjectId(projectId);
        bo.setModuleId(eSTModuleIDDef.Incident.getValue());
        var allFields = projectSystemFieldService.selectFieldList(bo);
        var pageFields = ProjectManager.getInstance(projectId).getProjectPageFields();
        return BaseLogic.getListviewFields(allFields, pageFields, selectedFieldIds, eSTModuleIDDef.Incident, option);
    }

    @Override
    public ItemHistoryModelVo getItemHistory(Integer projectId, Integer itemId) {
        if( projectId == 0){
            throw  new ServiceException("项目ID不能为0");
        }

        var itemHistoryModelVo = new ItemHistoryModelVo();
        var  historylist = itemHistoryMapper.selectVoList( new QueryWrapper<ItemHistory>().eq("project_id",projectId).eq("item_id",itemId));
        var projectManager =  ProjectManager.getInstance(projectId);
        if( historylist != null && !historylist.isEmpty()) {
            for (var history : historylist){
                history.setUserName(projectManager.getProjectMemberNickName( history.getUserId()));
                if(  history.getOwnerFrom() != null &&  history.getOwnerFrom() > 0)
                    history.setOwnerFromName( projectManager.getProjectMemberNickName( history.getOwnerFrom()));
                if(  history.getOwnerTo() != null &&  history.getOwnerTo() > 0)
                    history.setOwnerToName( projectManager.getProjectMemberNickName( history.getOwnerTo()));
                 if(  history.getStateFrom() != null &&  history.getStateFrom() > 0)
                    history.setStateFromName( projectManager.getWorkflowStateName( history.getStateFrom()));
                 if(  history.getStateTo() != null &&  history.getStateTo() > 0) {
                     var stateTo = projectManager.getWorkflowState(history.getStateTo());
                     if(  stateTo != null){
                       history.setStateToOptionId(stateTo.getStateOptionId());
                     }
                     history.setStateToName(projectManager.getWorkflowStateName(history.getStateTo()));
                 }
                  if(  history.getTransition() != null &&  history.getTransition() > 0)
                    history.setTransitionName( projectManager.getWorkflowTransitionName( history.getTransition()));
            }
             itemHistoryModelVo.setHistoryList(historylist);
        }
        var changelogList =  itemChangelogFieldMapper.selectItemChangelogField(projectId,itemId);
        if( changelogList != null && !changelogList.isEmpty()) {
            itemHistoryModelVo.setChangelogList(new ArrayList<>());
            var changelogIds =  changelogList.stream().map(BaseChangelogFieldVo::getChangelogId).toList();

            var changeTextFields = itemChangeTextMapper.selectVoList(new LambdaQueryWrapper<ItemChangeText>()
                .eq(ItemChangeText::getProjectId, projectId).eq(ItemChangeText::getItemId, itemId).in(ItemChangeText::getChangelogId, changelogIds));

            var changedByIds = changelogList.stream().map(BaseChangelogFieldVo::getChangedById).toList();
            var avatarList = userInfoMapper.getUserInfoAvatar(changedByIds);
            //group by change log id
             var changelogMap = changelogList.stream().collect(Collectors.groupingBy(BaseChangelogFieldVo::getChangelogId));

            String url = serverConfig.getUrl();
             for (var changelogId : changelogMap.keySet()) {
                 var changelogVo = new ItemChangelogVo();
                 changelogVo.setChangelogId(changelogId);
                 changelogVo.setChangelogFields(new ArrayList<>());
                 Integer index = 0;
                 for(var changelogField : changelogMap.get(changelogId)){
                     if( index == 0){//set changelog property
                         changelogVo.setChangedById(changelogField.getChangedById());
                         changelogVo.setChangedByUserName(projectManager.getProjectMemberNickName( changelogField.getChangedById()));
                         changelogVo.setLogTime(changelogField.getLogTime());
                         if( avatarList != null){
                            var avatar =  avatarList.stream().filter(a -> Objects.equals(a.getExternalUserId(), changelogField.getChangedById())).findFirst().orElse(null);
                             if( avatar != null) {
                                 var avatarUrl = avatar.getAvatarUrl();
                                 if( avatarUrl!= null && !avatarUrl.isEmpty() && !avatarUrl.contains("http"))
                                     avatarUrl  = url + avatarUrl;

                                 changelogVo.setChangedByUserAvatarUrl(avatarUrl);
                             }
                         }
                     }
                     changelogField.setFieldName(projectManager.getFieldName( changelogField.getFieldId()));
                     changelogField.setFieldType(projectManager.getFieldType( changelogField.getFieldId()));
                     if( projectManager.getFieldType( changelogField.getFieldId()) == eFieldTypeDef.PlainText.getValue() ||
                         projectManager.getFieldType( changelogField.getFieldId()) == eFieldTypeDef.RichText.getValue()){
                         var changeText  = changeTextFields.stream().filter( c -> c.getFieldId().equals(changelogField.getFieldId()) && c.getChangelogId().equals(changelogField.getChangelogId())).findFirst().orElse(null);
                         if(  changeText != null){
                             if( projectManager.getFieldType( changelogField.getFieldId()) == eFieldTypeDef.RichText.getValue()){
                                 changelogField.setChangeFromHtml(ValueConvert.encodeBase64(changeText.getChangeFrom()));
                                  changelogField.setChangeToHtml(ValueConvert.encodeBase64(changeText.getChangeTo()));
                             }
                             else {
                                 changelogField.setChangeFromPlainText(changeText.getChangeFrom());
                                 changelogField.setChangeToPlainText(changeText.getChangeTo());
                             }

                         }
                     }
                     changelogField.parseDescription4MultiLang(eCultureCode.ZH_CN);
                     changelogVo.getChangelogFields().add(changelogField);
                     index ++;
                 }

                 itemHistoryModelVo.getChangelogList().add(changelogVo);
             }
             //sort changelog list by changelog id desc
            itemHistoryModelVo.getChangelogList().sort(Comparator.comparingInt(ItemChangelogVo::getChangelogId).reversed());
        }
        return itemHistoryModelVo;
    }
    public ItemToDoListVo selectPageItemToDoList(ItemToDoListBo todoListBo, PageQuery pageQuery) {
        ItemToDoListVo itemToDoListVo = new ItemToDoListVo();
        if(todoListBo.getProjectIds() == null || todoListBo.getProjectIds().isEmpty())
            return itemToDoListVo;
        itemToDoListVo.setItemToDoList(new ArrayList<>());
        for (Integer projectId : todoListBo.getProjectIds()){
            var oneProjectItemTodoListVo = new ItemToDoListVo.ProjectItemToDoListVo();
            oneProjectItemTodoListVo.setProjectId(projectId);
            oneProjectItemTodoListVo.setSelectedFields(getListviewFields(projectId,1).getSelectedFields());
            var itemListBo = new ItemListBo();
            itemListBo.setProjectId(projectId);
            itemListBo.setOwnerIds( new ArrayList<Integer>());
            if(todoListBo.getOwnerId() != null)
                itemListBo.getOwnerIds().add(todoListBo.getOwnerId());
            itemListBo.setStateIds(new ArrayList<Integer>());
            if(todoListBo.getStateId() != null)
                itemListBo.getStateIds().add(todoListBo.getStateId());
            itemListBo.setSortFieldId(todoListBo.getSortFieldId());
            itemListBo.setFieldIds(oneProjectItemTodoListVo.getSelectedFields().stream().map(ProjectFieldVo::getFieldId).toList());
            oneProjectItemTodoListVo.setItemData(selectPageItemList(itemListBo, pageQuery));
            itemToDoListVo.getItemToDoList().add(oneProjectItemTodoListVo);
        }
        return itemToDoListVo;
    }
}
