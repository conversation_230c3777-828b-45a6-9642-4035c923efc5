package org.dromara.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ContactInfoChangelog;
import org.dromara.servicetrack.domain.bo.ContactInfoChangelogBo;
import org.dromara.servicetrack.domain.vo.ContactInfoChangelogVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * 联系人信息变更日志管理 数据层
 *
 * <AUTHOR>
 */
public interface ContactInfoChangelogMapper extends BaseMapperPlus<ContactInfoChangelog, ContactInfoChangelogVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ContactInfoChangelog> buildWrapper(ContactInfoChangelogBo bo) {
        LambdaQueryWrapper<ContactInfoChangelog> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ContactInfoChangelog::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, ContactInfoChangelog::getProjectId, bo.getProjectId());
        lqw.eq(bo.getChangelogId() != null, ContactInfoChangelog::getChangelogId, bo.getChangelogId());
        lqw.eq(bo.getContactId() != null, ContactInfoChangelog::getContactId, bo.getContactId());
        lqw.eq(bo.getChangedById() != null, ContactInfoChangelog::getChangedById, bo.getChangedById());
        return lqw;
    }

    /**
     * 根据项目ID和联系人ID查询变更日志列表
     *
     * @param projectId 项目ID
     * @param contactId 联系人ID
     * @return 变更日志列表
     */
    List<ContactInfoChangelogVo> selectByProjectAndContact(@Param("projectId") Integer projectId, @Param("contactId") Integer contactId);

    /**
     * 根据项目ID查询所有联系人变更日志
     *
     * @param projectId 项目ID
     * @return 变更日志列表
     */
    List<ContactInfoChangelogVo> selectByProjectId(@Param("projectId") Integer projectId);

    /**
     * 根据变更日志ID查询详细信息
     *
     * @param projectId   项目ID
     * @param changelogId 变更日志ID
     * @param contactId   联系人ID
     * @return 变更日志详情
     */
    ContactInfoChangelogVo selectByChangelogId(@Param("projectId") Integer projectId, 
                                              @Param("changelogId") Integer changelogId, 
                                              @Param("contactId") Integer contactId);
}
