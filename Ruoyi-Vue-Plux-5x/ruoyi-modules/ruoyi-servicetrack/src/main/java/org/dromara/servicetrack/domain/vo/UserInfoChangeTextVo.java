package org.dromara.servicetrack.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *  视图对象  user_info_changetext
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserInfoChangeTextVo extends BaseChangeTextVo{
    /**
     * modified by user id
     */
    private Integer modifiedById;
}
