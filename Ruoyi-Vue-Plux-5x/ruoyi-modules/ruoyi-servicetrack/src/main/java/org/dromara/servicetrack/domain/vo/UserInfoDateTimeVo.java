package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.UserInfoDateTime;

import java.util.Date;

/**
 *视图对象 user_info_datetime
 */
@Data
@EqualsAndHashCode(callSuper =true)
@AutoMapper(target = UserInfoDateTime.class)
public class UserInfoDateTimeVo extends UserInfoFieldVo{
    /**
     * 日期时间
     */
    private Date dateTime;

}
