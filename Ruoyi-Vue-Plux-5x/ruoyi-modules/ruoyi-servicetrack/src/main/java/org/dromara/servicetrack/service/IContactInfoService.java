package org.dromara.servicetrack.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.vo.ListViewFieldVo;
import org.dromara.servicetrack.domain.bo.ContactInfoBo;
import org.dromara.servicetrack.domain.bo.ContactInfoListBo;
import org.dromara.servicetrack.domain.vo.ContactInfoListVo;
import org.dromara.servicetrack.domain.vo.ContactInfoVo;

import java.util.Collection;

public interface IContactInfoService {
    /**
     * 分页查询Contact列表
     */
    TableDataInfo<ContactInfoListVo> selectPageUserList(ContactInfoListBo bo, PageQuery pageQuery );

    /**
     * 获取User字段详情
     */
    ContactInfoVo getContactInfoDetail(Integer projectId, Integer contactId);
    /**
     * 新增User
     */
    Integer insertByBo(ContactInfoBo bo);


    /**
     * 修改User info
     */
    Integer updateByBo(ContactInfoBo bo);

    /**
     * 校验并批量删除User信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Integer projectId,Integer customerId,Boolean isValid);

    /**
     * 获得ListView字段信息
     *  @param projectId 项目ID
     *  @param option 选项：0：可得到字段和选择字段，1：选择字段
     */
    ListViewFieldVo getListviewFields(Integer projectId, Integer option);

    boolean checkUserNameUnique(ContactInfoBo bo);
    boolean checkUserEmailUnique(ContactInfoBo user);
    boolean checkUserPhoneUnique(ContactInfoBo user);
}
