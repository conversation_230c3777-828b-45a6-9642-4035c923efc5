package org.dromara.servicetrack.controller;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.servicetrack.domain.bo.*;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldPcSelectionModelVo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldSelectionVo;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;
import org.dromara.common.servicetrack.service.*;
import org.dromara.common.web.core.BaseController;

import org.dromara.common.servicetrack.domain.vo.ProjectFieldVo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageFieldVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 字段管理
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/field")
public class ProjectFieldController extends BaseController{
    private final IProjectSystemFieldService projectSystemFieldService;
    private final IProjectPageFieldServiceEx projectPageFieldService;
    private final IProjectFieldSelectionService projectFieldSelectionService;
    private final IProjectFieldCustomService projectFieldCustomService;
    private final IProjectFieldPCService projectFieldPCService;
    /**
     * 字段列表
     */
    @GetMapping("/list")
    public R<List<ProjectFieldVo>> list(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                        @NotNull(message = "ModuleId不能为空") @RequestParam Integer moduleId) {
        ProjectSystemFieldBo bo = new ProjectSystemFieldBo();
        bo.setProjectId(projectId);
        bo.setModuleId(moduleId);
        return R.ok(projectSystemFieldService.selectFieldList(bo));
    }
    /**
     * 页面字段列表
     */
    @GetMapping("/pagefieldlist")
    public R<List<ProjectPageFieldVo>> list(ProjectPageFieldBo bo) {
        return R.ok(projectPageFieldService.selectPageFieldList(bo,true));
    }

    // 项目字段
    /**
     * 新增项目字段
     */
    @Log(title = "项目字段管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Integer> add(@Validated @RequestBody ProjectFieldCustomBo bo) {
        return R.ok(projectFieldCustomService.insertByBo(bo));
    }

    /**
     * 修改项目字段
     */
    @Log(title = "项目字段管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Integer> edit(@Validated(EditGroup.class) @RequestBody ProjectSystemFieldBo bo) {
        var fieldId = bo.getFieldId();
        if (FieldIdHelper.IsSystemField(fieldId)) {
            return R.ok(projectSystemFieldService.updateByBo(bo));
        }
        else {
            ProjectFieldCustomBo customBo = new ProjectFieldCustomBo();
            customBo.AssignField(bo);
            return R.ok(projectFieldCustomService.updateByBo(customBo));
        }
    }

    /**
     * 删除项目自定义字段
     *
     * @param ids 主键串
     */
    @Log(title = "项目字段管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectFieldCustomService.deleteWithValidByIds(List.of(ids), true));
    }
    //end of 项目字段

    // 项目字段选择
    /**
     * 根据字段id查询ChoiceIds
     */
    @GetMapping("/fieldchoiceList")
    public R<Map<Integer, List<ProjectFieldSelectionVo>> >getChoiceIdsByFieldId(FieldChoicesBo field) {
        return R.ok(projectFieldSelectionService.getChoiceIdsByFieldId(field));
    }
    /**
     * 项目字段选择列表
     */
    @GetMapping("/childFieldChoiceList")
    public R<Map<Integer, List<ProjectFieldSelectionVo>> > getChildFieldChoiceList(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
        @NotNull(message = "ParentFieldId不能为空") @RequestParam Integer parentFieldId,
        @NotNull(message = "parentFieldChoiceIds") @RequestParam List<Integer> parentFieldChoiceIds
    ) {
        return R.ok(projectFieldSelectionService.getChildFieldChoiceList(projectId, parentFieldId, parentFieldChoiceIds));
    }
    /**
     * 新增项目字段选择
     */
    @Log(title = "项目字段选择管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/selection")
    public R<Integer> addSelection(@Validated @RequestBody ProjectFieldSelectionBo bo) {
        return R.ok(projectFieldSelectionService.insertByBo(bo));
    }

    /**
     * 修改项目字段选择
     */
    @Log(title = "项目字段选择管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/selection")
    public R<Integer> editSelection(@Validated @RequestBody ProjectFieldSelectionBo bo) {
        return R.ok(projectFieldSelectionService.updateByBo(bo));
    }
    /**
     * 批量修改项目字段选择
     */
    @Log(title = "项目字段选择管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/selection/batch")
    public R<Boolean> batchEditSelection(@Validated @RequestBody ProjectFieldSelectionBatchBo bo) {
        return R.ok(projectFieldSelectionService.updateBatchByBo(bo));
    }
    /**
     * 删除项目自定义字段选择
     *
     * @param ids 主键串
     */
    @Log(title = "项目字段选择管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/selection/{ids}")
    public R<Void> removeSelection(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(projectFieldSelectionService.deleteWithValidByIds(List.of(ids), true));
    }
    /*
     * 排序项目字段选择顺序
     */
    @PostMapping("/selection/sort")
    public R<Void> sortSelection(@Validated @RequestBody ProjectSortBinderBo bo) {
        return toAjax(projectFieldSelectionService.sortSelection(bo));
    }
    //end of 项目字段选择

    //start of 项目字段父子关系
    /**
     * 项目字段父子关系列表
     */
    @GetMapping("/PCField/getOne")
    public R<ProjectFieldPcSelectionModelVo> getOnePCField(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                                 @NotNull(message = "ParentFieldId不能为空") @RequestParam Integer parentFieldId,
                                                                 @NotNull(message = "ChildFieldId不能为空") @RequestParam Integer childFieldId) {
        return R.ok(projectFieldPCService.getOneFieldPCSelection(projectId, parentFieldId, childFieldId));
    }
    /**
     * 项目字段父子选择项关系列表
     */
    @Log(title = "项目字段父子选择项关系管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/PCField")
    public R<Boolean> editPCFieldSelection(@Validated @RequestBody ProjectFieldPCBinderBo bo) {
        return R.ok(projectFieldPCService.updateByBo(bo));
    }
    /**
     * 删除项目字段父子关系
     *
     */
    @DeleteMapping("/PCField")
    public R<Void> removePCField(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                 @NotNull(message = "ChildFieldId不能为空") @RequestParam Integer childFieldId) {
        return toAjax(projectFieldPCService.deleteWithValid(projectId,  childFieldId, true));
    }
    //end of 项目字段父子关系
}
