package org.dromara.servicetrack.controller;

import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.servicetrack.domain.bo.FolderInfoBo;
import org.dromara.servicetrack.domain.vo.FolderTreeVo;
import org.dromara.servicetrack.service.IFolderTreeService;
import org.dromara.servicetrack.service.IReportTreeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件夹树结构管理
 *
 * <AUTHOR> fei
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/servicetrack/foldertree")
public class FolderTreeController extends BaseController  {
    private final IReportTreeService reportTreeService;
    private final IFolderTreeService folderTreeService;

    /**
     * 获取文件夹树结构
     */
    @GetMapping("/getFolders")
    public R<List<FolderTreeVo>> getFolders(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                      @NotNull(message = "ParentFolderId不能为空") @RequestParam Integer parentFolderId) {
        return R.ok(folderTreeService.getChildFoldersByParentId(projectId, parentFolderId));
    }
    /**
     * 新增 folder into folder tree
     */
    @Log(title = "folderTree管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/folder")
    public R<Integer> addFolder(@Validated @RequestBody FolderInfoBo bo) {
        return R.ok(folderTreeService.insertByBo(bo));
    }

    /**
     * edit folder
     */
    @Log(title = "folderTree管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/folder")
    public R<Integer> editFolder(@Validated @RequestBody FolderInfoBo bo) {
        return R.ok(folderTreeService.updateByBo(bo));
    }

    /**
     * 删除folders
     *
     */
    @Log(title = "FolderTree管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/folder")
    public R<Void> removeFolders(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                 @NotNull(message = "FolderIds不能为空") @RequestParam List<Integer> FolderIds) {
        return toAjax(folderTreeService.deleteWithFolderIds(projectId, FolderIds));
    }

    /**
     * 获取包含报告的文件夹树结构
     */
    @GetMapping("/getFolderWithReports")
    public R<List<FolderTreeVo>> getFolderWithReports(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                                      @NotNull(message = "ParentFolderId不能为空") @RequestParam Integer parentFolderId) {
        return R.ok(reportTreeService.getChildFoldersByParentIdWithReports(projectId, parentFolderId));
    }
    /**
     * 删除report tree folder
     *
     */
    @Log(title = "reportTree管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/report")
    public R<Void> removeReportTreeFolders(@NotNull(message = "ProjectId不能为空") @RequestParam Integer projectId,
                                           @NotNull(message = "FolderIds不能为空") @RequestParam List<Integer> FolderIds) {
        return toAjax(reportTreeService.deleteWithValidByIds(projectId, FolderIds,true));
    }

}
