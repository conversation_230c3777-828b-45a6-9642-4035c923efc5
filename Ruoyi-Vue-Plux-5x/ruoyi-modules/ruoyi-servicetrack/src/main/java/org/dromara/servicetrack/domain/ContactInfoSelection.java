package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 联系人信息选择字段对象 contact_info_selection
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("contact_info_selection")
public class ContactInfoSelection extends ContactInfoField {
    /**
     * 选择ID
     */
    @TableField("choice_id")
    private Integer choiceId;
}
