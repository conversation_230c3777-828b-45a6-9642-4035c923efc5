package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_setting")
public class ProjectSetting extends STBaseEntity {
    @TableId(value = "key_id")
    private Long id;

    @TableField(value = "project_id")
    private Integer projectId;

    @TableField(value = "setting_id")
    private Integer settingId;

    @TableField(value = "setting_name")
    private String settingName;

    @TableField(value = "setting_option")
    private Integer settingOption;

    @TableField(value = "setting_content")
    private String settingContent;
}