package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

/**
 * 系统设置对象 system_setting
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("system_setting")
public class SystemSetting extends STBaseEntity {
    /**
     * 键ID
     */
    @TableField(value = "key_id")
    private Long id;

    /**
     * 设置ID（主键）
     */
    @TableId(value = "setting_id")
    private Integer settingId;

    /**
     * 设置名称
     */
    @TableField(value = "setting_name")
    private String settingName;

    /**
     * 设置选项
     */
    @TableField(value = "setting_option")
    private Integer settingOption;

    /**
     * 设置内容
     */
    @TableField(value = "setting_content")
    private String settingContent;
}
