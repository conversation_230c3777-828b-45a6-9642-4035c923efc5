package org.dromara.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.servicetrack.domain.ReportInfo;
import org.dromara.servicetrack.domain.bo.ReportInfoBo;
import org.dromara.servicetrack.domain.vo.ReportInfoVo;
import org.dromara.servicetrack.mapper.ReportInfoMapper;
import org.dromara.servicetrack.service.IReportInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 报告信息管理 服务层实现
 *
 * <AUTHOR> Fei
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ReportInfoServiceImpl implements IReportInfoService {

    private final ReportInfoMapper baseMapper;
    private final TableSequenceManager tableSequenceManager;

    /**
     * 查询报告信息
     */
    @Override
    public ReportInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询报告信息列表
     */
    @Override
    public TableDataInfo<ReportInfoVo> queryPageList(ReportInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ReportInfo> lqw = buildQueryWrapper(bo);
        Page<ReportInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询报告信息列表
     */
    @Override
    public List<ReportInfoVo> queryList(ReportInfoBo bo) {
        LambdaQueryWrapper<ReportInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据项目ID和父文件夹ID获取报告列表
     */
    @Override
    public List<ReportInfoVo> getReportListByProjectIdAndFolderId(Integer projectId, Integer parentFolderId) {
        if (projectId == null || projectId <= 0) {
            throw new ServiceException("项目ID不能为空或小于等于0");
        }

        return baseMapper.getReportListByProjectIdAndFolderId(projectId, parentFolderId);
    }

    @Override
    public ReportInfoVo getReportInfo(Integer projectId, Integer reportId) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<ReportInfo>().eq(ReportInfo::getProjectId, projectId).eq(ReportInfo::getReportId, reportId));
    }

    /**
     * 根据项目ID和父文件夹ID分页获取报告列表
     */
    @Override
    public TableDataInfo<ReportInfoVo> getReportPageListByProjectIdAndFolderId(Integer projectId, Integer parentFolderId, PageQuery pageQuery) {
        if (projectId == null || projectId <= 0) {
            throw new ServiceException("项目ID不能为空或小于等于0");
        }

        LambdaQueryWrapper<ReportInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(ReportInfo::getProjectId, projectId);

        if (parentFolderId != null) {
            lqw.eq(ReportInfo::getFolderId, parentFolderId);
        } else {
            lqw.isNull(ReportInfo::getFolderId);
        }

        // 按报告名称排序
        lqw.orderByAsc(ReportInfo::getReportName);

        Page<ReportInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 新增报告信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertByBo(ReportInfoBo bo) {
        if(bo == null){
            throw new ServiceException("ReportInfoBo is null");
        }
        if(bo.getProjectId() == null || bo.getProjectId() == 0){
            throw new ServiceException("Project Id can't be 0");
        }
        if(bo.getFolderId() == null || bo.getFolderId() == 0){
            throw new ServiceException("Folder Id can't be 0");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        int userId = loginUser != null ? loginUser.getExternalUserId():0;
        bo.setReportId(tableSequenceManager.getNextSequence(SequenceTable.Report_Info,bo.getProjectId()));
        bo.setCreatedBy(userId);
        bo.setModifiedBy(userId);
        Date currentTime = DateUtils.getNowDate();
        bo.setCreatedDate(currentTime);
        bo.setModifiedDate(currentTime);
        ReportInfo add = MapstructUtils.convert(bo, ReportInfo.class);
        if(add == null){
            throw new ServiceException("ReportInfo is null");
        }
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag ? bo.getReportId() : 0;
    }

    /**
     * 修改报告信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateByBo(ReportInfoBo bo) {
        if(bo == null){
            throw new ServiceException("ReportInfoBo is null");
        }
        if(bo.getId() == null || bo.getId() == 0){
            throw new ServiceException("Id can't be null or 0");
        }
        if(bo.getFolderId() == null || bo.getFolderId() == 0){
            throw new ServiceException("Folder Id can't be 0");
        }

        bo.setModifiedDate(DateUtils.getNowDate());
        LoginUser loginUser = LoginHelper.getLoginUser();
        int userId = loginUser != null ? loginUser.getExternalUserId():0;
        bo.setModifiedBy(userId);
        ReportInfo update = MapstructUtils.convert(bo, ReportInfo.class);

        if(update == null){
            throw new ServiceException("ReportInfo is null");
        }
        //ignoring the private kye to update
        update.setProjectId(null);
        int reportId =  update.getReportId() != null ? update.getReportId():0;
        update.setReportId(null);
        return baseMapper.updateById(update) > 0 ? reportId : 0;
    }

    /**
     * 校验并批量删除报告信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 可以在这里添加删除前的校验逻辑
            for (Long id : ids) {
                ReportInfo reportInfo = baseMapper.selectById(id);
                if (reportInfo == null) {
                    throw new ServiceException("报告信息不存在，ID: " + id);
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 删除报告信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        ReportInfo reportInfo = baseMapper.selectById(id);
        if (reportInfo == null) {
            throw new ServiceException("报告信息不存在，ID: " + id);
        }
        return baseMapper.deleteById(id) > 0;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<ReportInfo> buildQueryWrapper(ReportInfoBo bo) {
        LambdaQueryWrapper<ReportInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ReportInfo::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, ReportInfo::getProjectId, bo.getProjectId());
        lqw.eq(bo.getReportId() != null, ReportInfo::getReportId, bo.getReportId());
        lqw.eq(bo.getReportType() != null, ReportInfo::getReportType, bo.getReportType());
        lqw.eq(bo.getTargetProjectId() != null, ReportInfo::getTargetProjectId, bo.getTargetProjectId());
        lqw.eq(bo.getFolderId() != null, ReportInfo::getFolderId, bo.getFolderId());
        lqw.like(StringUtils.isNotBlank(bo.getReportName()), ReportInfo::getReportName, bo.getReportName());
        lqw.like(StringUtils.isNotBlank(bo.getReportDescription()), ReportInfo::getReportDescription, bo.getReportDescription());
        lqw.eq(bo.getCreatedDate() != null, ReportInfo::getCreatedDate, bo.getCreatedDate());
        lqw.eq(bo.getCreatedBy() != null, ReportInfo::getCreatedBy, bo.getCreatedBy());
        lqw.eq(bo.getModifiedDate() != null, ReportInfo::getModifiedDate, bo.getModifiedDate());
        lqw.eq(bo.getModifiedBy() != null, ReportInfo::getModifiedBy, bo.getModifiedBy());
        return lqw;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ReportInfo entity) {
        if (entity.getProjectId() == null || entity.getProjectId() <= 0) {
            throw new ServiceException("项目ID不能为空或小于等于0");
        }
        if (entity.getReportId() == null || entity.getReportId() <= 0) {
            throw new ServiceException("报告ID不能为空或小于等于0");
        }
        if (entity.getTargetProjectId() == null || entity.getTargetProjectId() <= 0) {
            throw new ServiceException("目标项目ID不能为空或小于等于0");
        }
        if (StringUtils.isBlank(entity.getReportName())) {
            throw new ServiceException("报告名称不能为空");
        }
    }
}
