package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.servicetrack.domain.bo.ItemFieldBo;

import java.util.Date;
/**
 *  Item_change_text
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_changelog_text")
public class ItemChangeText extends BaseChangeText {
    /**
     * item id
     */
    private Integer itemId;
}
