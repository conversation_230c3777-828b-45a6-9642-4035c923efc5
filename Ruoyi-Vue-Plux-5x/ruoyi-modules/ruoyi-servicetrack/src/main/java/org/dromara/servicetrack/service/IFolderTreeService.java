package org.dromara.servicetrack.service;

import cn.hutool.core.lang.tree.Tree;
import org.dromara.servicetrack.domain.bo.FolderInfoBo;
import org.dromara.servicetrack.domain.bo.FolderTreeBo;
import org.dromara.servicetrack.domain.vo.FolderTreeVo;

import java.util.List;

/**
 * 文件夹树结构管理 服务层
 *
 * <AUTHOR>
 */
public interface IFolderTreeService {

    /**
     * 查询文件夹树结构
     *
     * @param id 文件夹树结构主键
     * @return 文件夹树结构
     */
    FolderTreeVo queryById(Long id);

    /**
     * 查询文件夹树结构列表
     *
     * @param bo 文件夹树结构
     * @return 文件夹树结构集合
     */
    List<FolderTreeVo> queryList(FolderTreeBo bo);

    /**
     * 新增文件夹树结构
     *
     * @param bo 文件夹树结构
     * @return 结果
     */
    Integer insertByBo(FolderInfoBo bo);

    /**
     * 修改文件夹树结构
     *
     * @param bo 文件夹树结构
     * @return 结果
     */
    Integer updateByBo(FolderInfoBo bo);

    /**
     * 校验并批量删除文件夹树结构信息
     *
     * @param ids 需要删除的文件夹树结构主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(List<Long> ids, Boolean isValid);

    Boolean deleteWithFolderIds(Integer projectId, List<Integer> folderIds);

    /**
     * 根据项目ID获取文件夹树结构
     *
     * @param projectId 项目ID
     * @return 树结构列表
     */
    List<Tree<Integer>> getFolderTreeByProjectId(Integer projectId);

    /**
     * 根据项目ID和根文件夹ID获取文件夹树结构
     *
     * @param projectId 项目ID
     * @param rootId 根文件夹ID，如果为null则获取所有树结构
     * @return 树结构列表
     */
    List<Tree<Integer>> getFolderTreeByProjectId(Integer projectId, Integer rootId);

    /**
     * 根据项目ID和父文件夹ID获取子文件夹列表
     *
     * @param projectId 项目ID
     * @param parentFolder 父文件夹ID
     * @return 子文件夹列表
     */
    List<FolderTreeVo> getChildFoldersByParentId(Integer projectId, Integer parentFolder);
}
