package org.dromara.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ItemTempInfo;
import org.dromara.servicetrack.domain.vo.ItemTempInfoListVo;
import org.dromara.servicetrack.domain.vo.ItemTempInfoVo;

import java.util.List;

/**
 * Item_Temp_info 数据层
 *
 * <AUTHOR> fei
 */
public interface ItemTempInfoMapper extends BaseMapperPlus<ItemTempInfo, ItemTempInfoVo> {
    List<ItemTempInfoListVo> getItemTempInfoList(@Param("projectId") Integer projectId, @Param("workProjectId") Integer workProjectId , @Param("groupId") Integer groupId, @Param("templateType") Integer templateType );
}
