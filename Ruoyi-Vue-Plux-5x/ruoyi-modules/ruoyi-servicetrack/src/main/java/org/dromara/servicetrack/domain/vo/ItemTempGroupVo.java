package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemSelection;
import org.dromara.servicetrack.domain.ItemTempGroup;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 *视图对象 item_temp_group
 */
@Data
@EqualsAndHashCode
@AutoMapper(target = ItemTempGroup.class)
public class ItemTempGroupVo implements Serializable  {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * group id
     */
    private Integer groupId;
    /**
     * group name
     */
    private String groupName;
    /**
     * group description
     */
    private String groupDesc;
    /**
     * parent group id
     */
    private Integer parentId;
    /**
     * display order
     */
    private Integer displayOrder;
}
