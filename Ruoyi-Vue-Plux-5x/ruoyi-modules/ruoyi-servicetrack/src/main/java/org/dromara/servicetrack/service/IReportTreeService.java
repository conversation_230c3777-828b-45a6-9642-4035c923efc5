package org.dromara.servicetrack.service;

import cn.hutool.core.lang.tree.Tree;
import org.dromara.servicetrack.domain.bo.FolderInfoBo;
import org.dromara.servicetrack.domain.bo.FolderTreeBo;
import org.dromara.servicetrack.domain.vo.FolderTreeVo;

import java.util.List;

/**
 * 报告树结构管理 服务层
 * 继承文件夹树服务，提供报告相关的树结构功能
 *
 * <AUTHOR>
 */
public interface IReportTreeService extends IFolderTreeService {

    /**
     * 根据项目ID获取报告文件夹树结构
     * 只包含包含报告的文件夹
     *
     * @param projectId 项目ID
     * @return 树结构列表
     */
    List<Tree<Integer>> getReportFolderTreeByProjectId(Integer projectId);

    /**
     * 根据项目ID和根文件夹ID获取报告文件夹树结构
     * 只包含包含报告的文件夹
     *
     * @param projectId 项目ID
     * @param rootId 根文件夹ID，如果为null则获取所有树结构
     * @return 树结构列表
     */
    List<Tree<Integer>> getReportFolderTreeByProjectId(Integer projectId, Integer rootId);

    /**
     * 根据项目ID和父文件夹ID获取子文件夹列表以及它们的报告列表
     *
     * @param projectId 项目ID
     * @param parentFolderId 父文件夹ID
     * @return 子文件夹列表
     */
    List<FolderTreeVo> getChildFoldersByParentIdWithReports(Integer projectId, Integer parentFolderId);
    /**
     * 获取指定文件夹下的报告数量
     *
     * @param projectId 项目ID
     * @param folderId 文件夹ID
     * @return 报告数量
     */
    Integer getReportCountByFolderId(Integer projectId, Integer folderId);

    /**
     * 检查文件夹是否包含报告（包括子文件夹）
     *
     * @param projectId 项目ID
     * @param folderId 文件夹ID
     * @return 是否包含报告
     */
    Boolean hasReportsInFolder(Integer projectId, Integer folderId);


    /**
     * 新增文件夹树结构
     *
     * @param bo 文件夹树结构
     * @return 结果
     */
    Integer insertByBo(FolderInfoBo bo);

    /**
     * 校验并批量删除文件夹树结构信息
     *
     */
    Boolean deleteWithValidByIds(Integer projectId, List<Integer> folderIds, Boolean isValid);
}
