package org.dromara.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.FolderInfo;
import org.dromara.servicetrack.domain.bo.FolderInfoBo;
import org.dromara.servicetrack.domain.vo.FolderInfoVo;

/**
 * 文件夹信息管理 数据层
 *
 * <AUTHOR>
 */
public interface FolderInfoMapper extends BaseMapperPlus<FolderInfo, FolderInfoVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<FolderInfo> buildWrapper(FolderInfoBo bo) {
        LambdaQueryWrapper<FolderInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, FolderInfo::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, FolderInfo::getProjectId, bo.getProjectId());
        lqw.eq(bo.getFolderId() != null, FolderInfo::getFolderId, bo.getFolderId());
        lqw.like(StringUtils.isNotBlank(bo.getFolderName()), FolderInfo::getFolderName, bo.getFolderName());
        lqw.eq(bo.getFolderType() != null, FolderInfo::getFolderType, bo.getFolderType());
        lqw.like(StringUtils.isNotBlank(bo.getFolderDescription()), FolderInfo::getFolderDescription, bo.getFolderDescription());
        lqw.eq(bo.getCreatedTime() != null, FolderInfo::getCreatedTime, bo.getCreatedTime());
        lqw.eq(bo.getCreatedBy() != null, FolderInfo::getCreatedBy, bo.getCreatedBy());

        return lqw;
    }
}
