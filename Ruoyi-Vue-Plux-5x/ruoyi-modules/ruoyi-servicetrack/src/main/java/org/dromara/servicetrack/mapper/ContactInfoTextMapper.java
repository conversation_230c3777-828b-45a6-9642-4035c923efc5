package org.dromara.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ContactInfoText;
import org.dromara.servicetrack.domain.bo.ContactInfoTextBo;
import org.dromara.servicetrack.domain.vo.ContactInfoTextVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;

import java.util.List;

/**
 * 联系人文本字段管理 数据层
 *
 * <AUTHOR>
 */
public interface ContactInfoTextMapper extends BaseMapperPlus<ContactInfoText, ContactInfoTextVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ContactInfoText> buildWrapper(ContactInfoTextBo bo) {
        LambdaQueryWrapper<ContactInfoText> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ContactInfoText::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, ContactInfoText::getProjectId, bo.getProjectId());
        lqw.eq(bo.getContactId() != null, ContactInfoText::getContactId, bo.getContactId());
        lqw.eq(bo.getFieldId() != null, ContactInfoText::getFieldId, bo.getFieldId());
        lqw.like(StringUtils.isNotBlank(bo.getText()), ContactInfoText::getText, bo.getText());
        return lqw;
    }

    /**
     * 根据项目ID和联系人ID查询文本字段列表
     *
     * @param projectId 项目ID
     * @param contactId 联系人ID
     * @return 文本字段列表
     */
    List<ContactInfoTextVo> selectByProjectAndContact(@Param("projectId") Integer projectId, @Param("contactId") Integer contactId);

    /**
     * 根据项目ID查询所有联系人文本字段
     *
     * @param projectId 项目ID
     * @return 文本字段列表
     */
    List<ContactInfoTextVo> selectByProjectId(@Param("projectId") Integer projectId);

    /**
     * 根据字段ID查询文本字段列表
     *
     * @param projectId 项目ID
     * @param contactId 联系人ID
     * @param fieldId   字段ID
     * @return 文本字段列表
     */
    List<ContactInfoTextVo> selectByFieldId(@Param("projectId") Integer projectId, 
                                           @Param("contactId") Integer contactId, 
                                           @Param("fieldId") Integer fieldId);
}
