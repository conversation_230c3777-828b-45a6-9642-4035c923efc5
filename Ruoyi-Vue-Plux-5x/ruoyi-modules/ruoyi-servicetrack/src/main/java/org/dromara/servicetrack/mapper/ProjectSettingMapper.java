package org.dromara.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ProjectSetting;
import org.dromara.servicetrack.domain.bo.ProjectSettingBo;
import org.dromara.servicetrack.domain.vo.ProjectSettingVo;

public interface ProjectSettingMapper extends BaseMapperPlus<ProjectSetting, ProjectSettingVo> {
    default LambdaQueryWrapper<ProjectSetting> buildWrapper(ProjectSettingBo bo) {
        LambdaQueryWrapper<ProjectSetting> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, ProjectSetting::getProjectId, bo.getProjectId());
        lqw.eq(bo.getSettingId() != null, ProjectSetting::getSettingId, bo.getSettingId());
        lqw.like(StringUtils.isNotBlank(bo.getSettingName()), ProjectSetting::getSettingName, bo.getSettingName());
        lqw.eq(bo.getSettingOption() != null, ProjectSetting::getSettingOption, bo.getSettingOption());
        return lqw;
    }
}