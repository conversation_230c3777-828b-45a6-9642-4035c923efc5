package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ContactInfoChangelog;

/**
 * 联系人信息变更日志视图对象 contact_info_changelog
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ContactInfoChangelog.class)
public class ContactInfoChangelogVo extends BaseChangelogVo {
    /**
     * 联系人ID
     */
    private Integer contactId;

    /**
     * 联系人名称（扩展字段，用于显示）
     */
    private String contactName;
}
