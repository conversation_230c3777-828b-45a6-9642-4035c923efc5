package org.dromara.servicetrack.service.impl;

import cn.hutool.core.lang.tree.Tree;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.dromara.servicetrack.domain.bo.FolderTreeBo;
import org.dromara.servicetrack.domain.vo.FolderTreeVo;
import org.dromara.servicetrack.service.IReportTreeService;

import java.util.List;

/**
 * ReportTreeServiceImpl使用示例
 * 展示如何使用继承的父类方法
 *
 * <AUTHOR>
 */
@Component
public class ReportTreeUsageExample {

    private final IReportTreeService reportTreeService;

    @Autowired
    public ReportTreeUsageExample(IReportTreeService reportTreeService) {
        this.reportTreeService = reportTreeService;
    }

    /**
     * 示例：使用父类的public方法
     */
    public void demonstrateParentMethods() {
        Integer projectId = 1;
        Integer rootId = 1;

        // 1. 使用父类的基本CRUD方法
        System.out.println("=== 使用父类的基本CRUD方法 ===");

        // 查询单个文件夹树结构（继承自父类）
        FolderTreeVo folderTree = reportTreeService.queryById(1L);
        System.out.println("查询单个文件夹树结构: " + folderTree);

        // 查询文件夹树结构列表（继承自父类）
        FolderTreeBo bo = new FolderTreeBo();
        bo.setProjectId(projectId);
        List<FolderTreeVo> folderTreeList = reportTreeService.queryList(bo);
        System.out.println("查询文件夹树结构列表数量: " + folderTreeList.size());

        // 新增文件夹树结构（继承自父类）
//        FolderTreeBo newBo = new FolderTreeBo();
//        newBo.setProjectId(projectId);
//        newBo.setParentFolder(1);
//        newBo.setChildFolder(8);
//        newBo.setDisplayOrder(1);
//        Boolean insertResult = reportTreeService.insertByBo(newBo);
//        System.out.println("新增文件夹树结构结果: " + insertResult);

        // 2. 使用父类的树结构方法
        System.out.println("\n=== 使用父类的树结构方法 ===");

        // 获取完整的文件夹树结构（继承自父类）
        List<Tree<Integer>> fullTree = reportTreeService.getFolderTreeByProjectId(projectId);
        System.out.println("完整文件夹树结构数量: " + fullTree.size());

        // 获取指定根节点的文件夹树结构（继承自父类）
        List<Tree<Integer>> subTree = reportTreeService.getFolderTreeByProjectId(projectId, rootId);
        System.out.println("指定根节点的文件夹树结构数量: " + subTree.size());

        // 获取子文件夹列表（继承自父类）
        List<FolderTreeVo> childFolders = reportTreeService.getChildFoldersByParentId(projectId, rootId);
        System.out.println("子文件夹列表数量: " + childFolders.size());

        // 3. 使用子类特有的报告相关方法
        System.out.println("\n=== 使用子类特有的报告相关方法 ===");

        // 获取包含报告的文件夹树结构（子类特有方法）
        List<Tree<Integer>> reportTree = reportTreeService.getReportFolderTreeByProjectId(projectId);
        System.out.println("包含报告的文件夹树结构数量: " + reportTree.size());

        // 获取指定文件夹下的报告数量（子类特有方法）
        Integer reportCount = reportTreeService.getReportCountByFolderId(projectId, 1);
        System.out.println("指定文件夹下的报告数量: " + reportCount);

        // 检查文件夹是否包含报告（子类特有方法）
        Boolean hasReports = reportTreeService.hasReportsInFolder(projectId, 1);
        System.out.println("文件夹是否包含报告: " + hasReports);
    }

    /**
     * 示例：组合使用父类和子类方法
     */
    public void demonstrateCombinedUsage() {
        Integer projectId = 1;

        System.out.println("=== 组合使用父类和子类方法 ===");

        // 1. 使用父类方法获取完整树结构
        List<Tree<Integer>> fullTree = reportTreeService.getFolderTreeByProjectId(projectId);

        // 2. 遍历树结构，使用子类方法检查每个文件夹的报告情况
        for (Tree<Integer> tree : fullTree) {
            processTreeNode(tree, projectId);
        }
    }

    /**
     * 递归处理树节点
     */
    private void processTreeNode(Tree<Integer> node, Integer projectId) {
        // 使用子类方法获取报告数量
        Integer reportCount = reportTreeService.getReportCountByFolderId(projectId, node.getId());

        // 使用子类方法检查是否包含报告
        Boolean hasReports = reportTreeService.hasReportsInFolder(projectId, node.getId());

        System.out.println(String.format("文件夹[%s] - ID: %d, 直接报告数: %d, 包含报告: %s",
            node.getName(), node.getId(), reportCount, hasReports));

        // 递归处理子节点
        if (node.getChildren() != null) {
            for (Tree<Integer> child : node.getChildren()) {
                processTreeNode(child, projectId);
            }
        }
    }
}
