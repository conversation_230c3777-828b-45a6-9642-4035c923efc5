package org.dromara.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.UserInfo;
import org.dromara.servicetrack.domain.vo.ContactInfoListVo;
import org.dromara.servicetrack.domain.vo.ContactInfoVo;
import org.dromara.servicetrack.domain.vo.STUserInfoVo;
import org.dromara.servicetrack.domain.vo.UserInfoListVo;

import java.util.List;


/**
 * 用户管理 数据层
 *
 * <AUTHOR> fei
 */
public interface UserInfoMapper extends BaseMapperPlus<UserInfo, STUserInfoVo> {
   /**
    * 分页查询用户列表
    */
    Page<UserInfoListVo> selectPageUserList(@Param("page") Page<UserInfo> page, @Param(Constants.WRAPPER) Wrapper<UserInfo> queryWrapper,
                                             @Param("projectId") Integer projectId, @Param("userSqlSegment") String userSqlSegment,
                                            @Param("sortFieldId") Integer sortFieldId, @Param("textFieldIds") List<Integer> textFieldIds,
                                            @Param("dateTimeFieldIds") List<Integer> dateTimeFieldIds,@Param("selectionFieldIds") List<Integer> selectionFieldIds);

    /**
     * 分页查询联系人列表
     */
    Page<ContactInfoListVo> selectPageUserList4Contact(@Param("page") Page<UserInfo> page, @Param(Constants.WRAPPER) Wrapper<UserInfo> queryWrapper,
                                                       @Param("projectId") Integer projectId, @Param("customerId") Integer customerId, @Param("userSqlSegment") String userSqlSegment,
                                                       @Param("sortFieldId") Integer sortFieldId, @Param("textFieldIds") List<Integer> textFieldIds,
                                                       @Param("dateTimeFieldIds") List<Integer> dateTimeFieldIds, @Param("selectionFieldIds") List<Integer> selectionFieldIds);
    /**
     * 根据用户id查询用户类型
     * @param userId
     * @return 用户类型
     */
    Integer getSTUserTypeById(@Param("userId") Long userId);
    /**
    * 根据项目id和用户id查询用户信息
     */
    STUserInfoVo getUserInfoDetail(@Param("projectId") Integer projectId, @Param("userId") Integer userId);

    /**
     * 根据项目id和用户id查询联系人信息
     */
    ContactInfoVo getUserInfoDetail4Contact(@Param("projectId") Integer projectId, @Param("contactId") Integer contactId);

    List<STUserInfoVo> getUserInfoAvatar( @Param("userIds")  List<Integer> userIds);
}
