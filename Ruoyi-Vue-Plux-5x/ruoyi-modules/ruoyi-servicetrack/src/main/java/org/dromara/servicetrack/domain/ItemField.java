package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 条目字段对象 item field base class
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ItemField extends BaseField {

    /**
     * item id
     */
    @TableField(value = "item_id")
    private Integer itemId;
}
