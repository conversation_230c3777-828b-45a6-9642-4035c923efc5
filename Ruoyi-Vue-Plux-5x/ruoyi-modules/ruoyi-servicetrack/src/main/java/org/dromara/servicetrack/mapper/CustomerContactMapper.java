package org.dromara.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.CustomerContact;
import org.dromara.servicetrack.domain.bo.CustomerContactBo;
import org.dromara.servicetrack.domain.vo.CustomerContactVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * 客户联系人关联管理 数据层
 *
 * <AUTHOR>
 */
public interface CustomerContactMapper extends BaseMapperPlus<CustomerContact, CustomerContactVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<CustomerContact> buildWrapper(CustomerContactBo bo) {
        LambdaQueryWrapper<CustomerContact> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getKeyId() != null, CustomerContact::getKeyId, bo.getKeyId());
        lqw.eq(bo.getProjectId() != null, CustomerContact::getProjectId, bo.getProjectId());
        lqw.eq(bo.getCustomerId() != null, CustomerContact::getCustomerId, bo.getCustomerId());
        lqw.eq(bo.getContactId() != null, CustomerContact::getContactId, bo.getContactId());
        return lqw;
    }

    /**
     * 根据项目ID和客户ID查询联系人列表
     *
     * @param projectId  项目ID
     * @param customerId 客户ID
     * @return 联系人列表
     */
    List<CustomerContactVo> selectContactsByCustomer(@Param("projectId") Integer projectId, @Param("customerId") Integer customerId);

    /**
     * 根据项目ID和联系人ID查询客户列表
     *
     * @param projectId 项目ID
     * @param contactId 联系人ID
     * @return 客户列表
     */
    List<CustomerContactVo> selectCustomersByContact(@Param("projectId") Integer projectId, @Param("contactId") Integer contactId);

    /**
     * 根据项目ID查询所有客户联系人关联
     *
     * @param projectId 项目ID
     * @return 客户联系人关联列表
     */
    List<CustomerContactVo> selectByProjectId(@Param("projectId") Integer projectId);

    /**
     * 检查客户联系人关联是否存在
     *
     * @param projectId  项目ID
     * @param customerId 客户ID
     * @param contactId  联系人ID
     * @return 关联记录数量
     */
    int checkRelationExists(@Param("projectId") Integer projectId, 
                           @Param("customerId") Integer customerId, 
                           @Param("contactId") Integer contactId);

    /**
     * 批量删除客户的联系人关联
     *
     * @param projectId  项目ID
     * @param customerId 客户ID
     * @param contactIds 联系人ID列表
     * @return 删除的记录数
     */
    int deleteCustomerContacts(@Param("projectId") Integer projectId, 
                              @Param("customerId") Integer customerId, 
                              @Param("contactIds") List<Integer> contactIds);
}
