package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ContactInfoText;

/**
 * 联系人信息文本字段视图对象 contact_info_text
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ContactInfoText.class)
public class ContactInfoTextVo extends ContactInfoFieldVo{
    /**
     * 文本内容
     */
    private String text;

    /**
     * 联系人名称（扩展字段，用于显示）
     */
    private String contactName;
}
