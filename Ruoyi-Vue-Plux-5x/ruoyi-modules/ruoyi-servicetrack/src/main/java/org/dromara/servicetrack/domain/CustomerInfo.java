package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 客户信息对象 customer_info
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("customer_info")
public class CustomerInfo extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * 项目ID
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * 客户ID
     */
    @TableField(value = "customer_id")
    private Integer customerId;

    /**
     * 客户名称
     */
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 客户状态
     */
    @TableField(value = "customer_status")
    private String customerStatus;

    /**
     * 客户备注
     */
    @TableField(value = "customer_notes")
    private String customerNotes;

    /**
     * 创建者
     */
    @TableField(value = "created_by")
    private Integer createdBy;
    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * 修改者
     */
    @TableField(value = "modified_by")
    private Integer modifiedBy;
    /**
     * 修改时间
     */
    @TableField(value = "modified_time")
    private Date modifiedTime;

    @TableField(value = "customer_type")
    private Integer customerType;

    @TableField(value = "customer_industry")
    private Integer customerIndustry;

    @TableField(value = "customer_level")
    private Integer customerLevel;

    @TableField(value = "customer_address")
    private String customerAddress;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableField(value = "del_flag")
    private String delFlag;
}
