package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
/**
 *  Item_temp_group
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_temp_group")
public class ItemTempGroup extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * group id
     */
    @TableField(value = "group_id")
    private Integer groupId;

    /**
     * group name
     */
    @TableField(value = "group_name")
    private String groupName;

    /**
     * parent id
     */
    @TableField(value = "parent_id")
    private Integer parentId;

    /**
     * group description
     */
    @TableField(value = "group_desc")
    private String groupDesc;

    /**
     * display order
     */
    @TableField(value = "display_order")
    private Integer displayOrder;
}
