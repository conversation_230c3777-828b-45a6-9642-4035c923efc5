package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *  user_info_changelog
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("user_info_changelog")
public class UserInfoChangelog extends BaseChangelog{
    /**
     * user id
     */
    @TableField(value = "user_id")
    private Integer userId;
}
