package org.dromara.servicetrack.logic;


import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.UserConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.servicetrack.constant.*;
import org.dromara.common.servicetrack.domain.NotificationEmailQueue;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldSelectionVo;
import org.dromara.common.servicetrack.domain.vo.WorkflowStateAttributesVo;
import org.dromara.common.servicetrack.domain.vo.WorkflowTransitionNextStateVo;
import org.dromara.common.servicetrack.sequence.SequenceTable;
import org.dromara.common.servicetrack.sequence.TableSequenceManager;
import org.dromara.common.servicetrack.domain.vo.WorkflowTransitionStateVo;
import org.dromara.common.servicetrack.infrastructure.IFieldValue;
import org.dromara.common.servicetrack.logic.fieldvalue.*;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.service.INotificationService;
import org.dromara.common.servicetrack.utils.ValueConvert;
import org.dromara.servicetrack.domain.ItemInfo;
import org.dromara.servicetrack.domain.bo.*;
import org.dromara.servicetrack.domain.vo.ItemInfoVo;
import org.dromara.common.servicetrack.domain.bo.FieldValueBo;
import org.dromara.servicetrack.domain.vo.ItemTimestampSimpleVo;
import org.dromara.servicetrack.model.field.TFieldValueVo;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 条目业务对象 item logic
 *
 * <AUTHOR> fei
 */
@Slf4j
public class ItemLogic extends AbstractEntityLogic<ItemInfoBo, ItemInfoVo>  {
    private final TableSequenceManager tableSequenceManager;
    private final ItemInfoVo originalItemInfoVo;
    private final INotificationService notificationService;
    public ItemLogic(TableSequenceManager tableSequenceManager, Integer UserId, ItemInfoVo originalItemInfVo) {
        super(tableSequenceManager, UserId,originalItemInfVo,InfoVoType.ITEM_INFO);
        this.tableSequenceManager = tableSequenceManager;
        this.originalItemInfoVo = originalItemInfVo;
        this.notificationService = SpringUtils.getBean(INotificationService.class);
    }

    /**
     * 将ItemInfoBo转换为ItemInfo
     *
     * @param bo ItemInfoBo对象
     * @return 转换后的ItemInfo对象
     */
    public ItemInfo ConvertToItemInfo(ItemInfoBo bo) {
        try {
            if (bo == null) {
                throw new ServiceException("ItemInfoBo cannot be null");
            }
            if (bo.getProjectId() == null) {
                throw new ServiceException("ProjectId cannot be null");
            }
            boolean isNew =  bo.getItemId() == null || bo.getItemId() == 0;
            int itemId = isNew ? ( (bo.getPreAllocatedItemId() != null && bo.getPreAllocatedItemId() > 0) ? bo.getPreAllocatedItemId() : tableSequenceManager.getNextSequence(SequenceTable.Item_Info, bo.getProjectId()))
                            :bo.getItemId();
            bo.setItemId(itemId);
            bo.setDisplayId(UserConstants.ITEM_DISPLAY_ID_PREFIX + itemId);
            Date currentTime = DateUtils.getNowDate();
            bo.setModifiedTime(currentTime);
            bo.setAssignedTime(currentTime);
            if( isNew ) {
                bo.setCreatedBy(UserId);
                bo.setCreatedTime(currentTime);
            }
            bo.setModifiedBy(UserId);

            //if it is new, need to generate a changelog
            if( isNew)
                generateSubmitOrDeleteChangelog(bo,true);

            var fieldValues = bo.getFields();
            if( fieldValues != null && !fieldValues.isEmpty())
            {
                //remove duplicate fields while keeping the last occurrence of each fieldId
                Map<Integer, FieldValueBo> uniqueFields = new java.util.LinkedHashMap<>();
                for (var field : fieldValues) {
                    uniqueFields.put(field.getFieldId(), field);
                }
                fieldValues.clear();
                fieldValues.addAll(uniqueFields.values());

                for (var fieldValue : fieldValues){
                    setFieldValue(bo,fieldValue);
                }
            }
            return MapstructUtils.convert(bo, ItemInfo.class);
        } catch (ServiceException e) {
            throw new ServiceException("Failed to convert ItemInfoBo to ItemInfo:" + e.getMessage());
        }
    }

    @Override
    protected int getProjectId(ItemInfoBo bo) {
        return bo.getProjectId();
    }

    @Override
    protected int getId(ItemInfoBo bo) {
        return bo.getItemId();
    }

    @Override
    protected void setAllModuleSystemFieldValue(ItemInfoBo bo,Integer fieldId,IFieldValue newFieldValue){
        if( fieldId == eSystemFieldDef.Title.getValue()){
            bo.setItemTitle(newFieldValue.toCustomFieldFormatString());
        }
        else  if( fieldId == eSystemFieldDef.Status.getValue() ){
            bo.setStateId(ValueConvert.readInt(newFieldValue.getRawValue()));
            if(this.originalItemInfoVo != null) {
                this.originalItemInfoVo.setStateId(bo.getStateId());
                //check if this state is closed
                if( ProjectManager.getInstance(bo.getProjectId()).isWorkflowStateClosed(bo.getStateId())){
                    bo.setClosedBy(UserId);
                    bo.setClosedTime(DateUtils.getNowDate());
                }
            }
        }
        else if( fieldId == eSystemFieldDef.Owner.getValue()){
            bo.setOwnerId(ValueConvert.readInt(newFieldValue.getRawValue()));
            if(this.originalItemInfoVo != null)
                this.originalItemInfoVo.setOwnerId(bo.getOwnerId());
        }
        else if ( fieldId == eSystemFieldDef.Type.getValue()){
            bo.setTypeId(ValueConvert.readInt(newFieldValue.getRawValue()));
        }
        else if( fieldId == eSystemFieldDef.Employee.getValue()){
            bo.setEmployeeId(ValueConvert.readInt(newFieldValue.getRawValue()));
        }
        else if( fieldId == eSystemFieldDef.Customer.getValue()){
            bo.setCustomerId(ValueConvert.readInt(newFieldValue.getRawValue()));
        }
        else if( fieldId == eSystemFieldDef.Description.getValue()){
            setCustomFieldValue(bo,fieldId,newFieldValue);
        }
    }
    @Override
    protected void setIndividualSystemFieldValue(ItemInfoBo bo,Integer fieldId,IFieldValue newFieldValue){
        //to do
    }

    @Override
    protected boolean  getAllModuleSystemFieldOriginalValue(Integer fieldId,IFieldValue oldFieldValue){
        if( oldFieldValue == null || this.originalItemInfoVo == null)
            return true;
        if( fieldId == eSystemFieldDef.Title.getValue()){
            oldFieldValue.readValueFromDB(this.originalItemInfoVo.getItemTitle());
        }
        else  if( fieldId == eSystemFieldDef.Owner.getValue() ){
            oldFieldValue.readValueFromDB( this.originalItemInfoVo.getOwnerId());
        }
        else if( fieldId == eSystemFieldDef.Status.getValue()){
            oldFieldValue.readValueFromDB(this.originalItemInfoVo.getStateId());
        }
        else if ( fieldId == eSystemFieldDef.Type.getValue()){
            oldFieldValue.readValueFromDB( this.originalItemInfoVo.getTypeId());
        }
        else if( fieldId == eSystemFieldDef.Employee.getValue()){
            oldFieldValue.readValueFromDB(this.originalItemInfoVo.getEmployeeId());
        }
        else if( fieldId == eSystemFieldDef.Customer.getValue()){
            oldFieldValue.readValueFromDB(this.originalItemInfoVo.getCustomerId());
        }
        else if( fieldId == eSystemFieldDef.Description.getValue()){
            return getCustomFieldOriginalValue(this.originalItemInfoVo.getProjectId(),fieldId,oldFieldValue);
        }
        return true;
    }
    @Override
    protected boolean getIndividualSystemFieldOriginalValue(Integer fieldId,IFieldValue oldFieldValue){
        //to do
        return false;
    }

    /**
     * 从条目转换后的字段获取字段Vo值，包括系统字段和自定义字段
     */
    public void retrieveItemFieldValues(Integer workProjectId,boolean ignoreWorkflowPermission,Integer transitionId){
        if( this.originalItemInfoVo == null)
            return ;
       //Get all module system fields
        if( this.originalItemInfoVo.getFields() == null)
            this.originalItemInfoVo.setFields(new ArrayList<>());

        Integer projectId = this.originalItemInfoVo.getProjectId();
        //get timestamp fields firstly.
        getTimestampFieldValues();

        for (var enumfieldId : FieldIdHelper.AllModuleSystemFields){
            int fieldId = enumfieldId.getValue();
            if( fieldId == eSystemFieldDef.Description.getValue())//using getTextFields to handle description field
                continue;
            if( fieldId == eSystemFieldDef.Attachment.getValue()){

                var attachments = this.originalItemInfoVo.getAttachments();
                if( attachments != null && !attachments.isEmpty()){

                    TFieldValueVo attachmentFieldValue = new TFieldValueVo();
                    attachmentFieldValue.setFieldId(fieldId);
                    attachmentFieldValue.setValue(this.originalItemInfoVo.getAttachmentJsonValue());
                  originalItemInfoVo.getFields().add(attachmentFieldValue);
                }
                continue;
            }
            FieldValueHandler fieldValueHandler = new FieldValueHandler(projectId, eSTModuleIDDef.Incident, fieldId,workProjectId);
            IFieldValue oldFieldValue = fieldValueHandler.createFieldValue();
            if( oldFieldValue != null && getFieldOriginalValue(projectId,fieldId,oldFieldValue)){
                if(FieldIdHelper.isTimestampField(projectId,fieldId)) {
                    continue;
                }
                TFieldValueVo fieldValueVo = new TFieldValueVo();
                fieldValueVo.setFieldId(fieldId);
                fieldValueVo.setValue(oldFieldValue.getDisplayValue());
                fieldValueVo.setChoiceId(oldFieldValue.getRawValue());
                originalItemInfoVo.getFields().add(fieldValueVo);
            }
        }
        //Get individual system fields
//        for (var fieldId : FieldIdHelper.IndividualSystemFields){
//
//        }
        //Get custom fields
        super.retrieveItemCustomFieldValues(projectId,workProjectId);

        //get workflow transition state
        if(this.originalItemInfoVo.getStateId() == null)//set default state id
            this.originalItemInfoVo.setStateId(0);

        if( this.originalItemInfoVo.getStateId() != null){
            if (!ignoreWorkflowPermission){
                checkWorkflowStatePermission(workProjectId);
            }
           var projectManager = ProjectManager.getInstance(workProjectId);

            if( transitionId == null || transitionId == 0) {
                WorkflowTransitionStateVo nextWorkflowTransitionState = projectManager.getWorkflowTransitionNextState(this.originalItemInfoVo.getStateId(), UserId);
                if (nextWorkflowTransitionState != null) {
                    this.originalItemInfoVo.setNextWorkflowTransitionState(nextWorkflowTransitionState);
                }
            }
            else {
                var nextWorkflowTransitionState = new WorkflowTransitionStateVo();
                nextWorkflowTransitionState.setNextStates(new ArrayList<>());
                nextWorkflowTransitionState.getNextStates().add(new WorkflowTransitionNextStateVo(
                    transitionId,
                    projectManager.getWorkflowTransitionName(transitionId),
                    this.originalItemInfoVo.getStateId(),
                    "",
                    0,
                    0,
                    projectManager.getWorkflowTransitionFieldIds4Mandatory(transitionId),
                    new WorkflowStateAttributesVo())
                );
                this.originalItemInfoVo.setNextWorkflowTransitionState(nextWorkflowTransitionState);
            }
        }
    }
    private void checkWorkflowStatePermission(Integer workProjectId){
        if( this.originalItemInfoVo == null)
            return;
        if( this.originalItemInfoVo.getStateId() == null)
            return;
        var projectManager = ProjectManager.getInstance(workProjectId);
        this.originalItemInfoVo.setStateAttributes(projectManager.getWorkflowStateAttributes(this.originalItemInfoVo.getStateId(),UserId));
    }
    private void getTimestampFieldValues(){
        if( this.originalItemInfoVo == null)
            return;
        var itemTimestamps = this.originalItemInfoVo.getTimestamps();
        if( itemTimestamps == null || itemTimestamps.isEmpty())
            return;

        for( var itemTimestamp : itemTimestamps){
            int fieldId = itemTimestamp.getFieldId();
            var matchedFieldValue = this.originalItemInfoVo.getFields().stream().filter(field -> field.getFieldId() == fieldId).findFirst().orElse(null);
            if( matchedFieldValue == null) {
                matchedFieldValue = new TFieldValueVo();
                matchedFieldValue.setFieldId(fieldId);
                matchedFieldValue.setValue("");
                originalItemInfoVo.getFields().add(matchedFieldValue);
            }
            if(Objects.equals(itemTimestamp.getFieldId(), fieldId)){
                if(matchedFieldValue.getAdditionInfo() == null)
                    matchedFieldValue.setAdditionInfo((new ArrayList<>()));

                var itemTimestampSimpleVo = new ItemTimestampSimpleVo(itemTimestamp);
                matchedFieldValue.getAdditionInfo().add(itemTimestampSimpleVo);
            }
        }
    }
    @Override
    protected void getTimestampFieldValue(Integer fieldId, IFieldValue oldFieldValue) {
        if( this.originalItemInfoVo == null || this.originalItemInfoVo.getFields() == null || oldFieldValue == null)
            return;
        var timestampFieldValue = this.originalItemInfoVo.getFields().stream().filter(field -> field.getFieldId() == fieldId).findFirst().orElse(null);
        if( timestampFieldValue != null) {
            if (timestampFieldValue.getAdditionInfo() == null)
                return;
            StringBuilder sb = new StringBuilder();
            for (var itemTimestampSimpleVo : timestampFieldValue.getAdditionInfo()) {
                var itemTimestampVo = (ItemTimestampSimpleVo) itemTimestampSimpleVo;
                if (itemTimestampVo.getContent() != null && !itemTimestampVo.getContent().isEmpty()) {
                    sb.append(itemTimestampVo.getContent()).append("\r\n");
                }
                oldFieldValue.readValueFromDB(sb.toString());
            }
        }
    }
    public void sendNotification(Integer projectId, Integer itemId, boolean isNewItem){
        if( !ProjectManager.getInstance(projectId).isEmailEnabled()){
           return;
        }
        List<Integer> ruleTypeList = getRuleTypeList(isNewItem);
        var ruleItems = ProjectManager.getInstance(projectId).getNotificationRule(eSTModuleIDDef.Incident.getValue(),ruleTypeList);
        if( ruleItems == null || ruleItems.isEmpty())
            return;

        for (var ruleItem : ruleItems) {
            boolean needSendNotification = false;
            if(!isNewItem){//for updated item, just check if owner or state has changed
                if( this.changedFieldIds.contains(eSystemFieldDef.Owner.getValue()) || this.changedFieldIds.contains(eSystemFieldDef.Status.getValue())){
                    needSendNotification = true;

                }
            }
            else {
                needSendNotification = true;
            }
            if(needSendNotification){
                var projectManager = ProjectManager.getInstance(projectId);
                NotificationEmailQueue queue = new NotificationEmailQueue();
                queue.setProjectId(projectId);
                queue.setQueueId(tableSequenceManager.getNextSequence(SequenceTable.Notification_Email_Queue));
                queue.setRuleId(ruleItem.getRuleId());
                queue.setObjectType(eSTModuleIDDef.Incident.getValue());
                queue.setObjectId(itemId);
                queue.setCreatedTime(DateUtils.getNowDate());
                queue.setEmailSubject(parseFieldInNotification(projectId,ruleItem.getEmailTemplate().getEmailSubject()));
                queue.setEmailBody(parseFieldInNotification(projectId,ruleItem.getEmailTemplate().getEmailBody()));
                if(ruleItem.getRecipients() != null && !ruleItem.getRecipients().isEmpty()){
                    List<String> emailList = new ArrayList<>();
                    for (var recipient : ruleItem.getRecipients()) {
                        if (recipient.getUserId() == null || recipient.getUserId() == 0)
                            continue;
                        if (recipient.getUserType() == eUserTypeDef.IndividualUser.getValue()) {
                            String email = null;
                            if (recipient.getUserId() < 0) {
                                if (recipient.getUserId().equals(stConstant.Owner_SUBMITTER)) {
                                    email = projectManager.getSysUserEmail(this.originalItemInfoVo.getCreatedBy());
                                } else if (recipient.getUserId().equals(stConstant.Owner_CURRENT)) {
                                    email = projectManager.getSysUserEmail(this.originalItemInfoVo.getOwnerId());
                                }
                            } else {
                                email = projectManager.getSysUserEmail(recipient.getUserId());
                            }
                            if (email != null && !email.isEmpty() && !emailList.contains(email)) {
                                emailList.add(email);
                            }
                        } else if (recipient.getUserType() == eUserTypeDef.AccountType.getValue()) {
                            var accountTypeUserIds = projectManager.getAccountUserList(recipient.getUserId());
                            if (accountTypeUserIds != null && !accountTypeUserIds.isEmpty()) {
                                for (var userId : accountTypeUserIds) {
                                    String email = projectManager.getSysUserEmail(userId);
                                    if (email != null && !email.isEmpty() && !emailList.contains(email)) {
                                        emailList.add(email);
                                    }
                                }
                            }
                        } else if (recipient.getUserType() == eUserTypeDef.Group.getValue()) {
                            var groupId = stConstant.Project_Group_Offset - recipient.getUserId();
                            var groupUserIds = projectManager.getProjectGroupUserList(groupId);
                            if (groupUserIds != null && !groupUserIds.isEmpty()) {
                                for (var userId : groupUserIds) {
                                    String email = projectManager.getSysUserEmail(userId);
                                    if (email != null && !email.isEmpty() && !emailList.contains(email)) {
                                        emailList.add(email);
                                    }
                                }
                            }
                        }
                    }
                    if( !emailList.isEmpty())
                      queue.setRecipient( String.join(",", emailList));

                }
                queue.setRecipientEmailAddress(ruleItem.getCcEmailAddress());
                notificationService.insertQueue(queue);
            }
        }
    }

    private List<Integer> getRuleTypeList(boolean isNewItem) {
        List<Integer> ruleTypeList = new ArrayList<>();
        if(isNewItem){
            //send email for new item
            ruleTypeList.add(eNotificationRuleTypeDef.NewItem.getValue());
        }
        else{
            //send email for updated item
            var changedFieldValues = new ArrayList<TFieldValueVo>();
            if( this.changedFieldIds.contains(eSystemFieldDef.Owner.getValue()) ){
                ruleTypeList.add(eNotificationRuleTypeDef.OwnerChanged.getValue());
                //set owner value into field value--if support other normal fields for notification, then need to get item details again.
                var fieldValue = new TFieldValueVo();
                fieldValue.setFieldId(eSystemFieldDef.Owner.getValue());
                fieldValue.setChoiceId(this.originalItemInfoVo.getOwnerId());
                fieldValue.setValue(ProjectManager.getInstance(this.originalItemInfoVo.getProjectId()).getProjectMemberNickName(this.originalItemInfoVo.getOwnerId()));
                changedFieldValues.add(fieldValue);
            }
            if( this.changedFieldIds.contains(eSystemFieldDef.Status.getValue())){
                ruleTypeList.add(eNotificationRuleTypeDef.stateChanged.getValue());
                //set state value into field value --if support other normal fields for notification, then need to get item details again.
                var fieldValue = new TFieldValueVo();
                fieldValue.setFieldId(eSystemFieldDef.Status.getValue());
                fieldValue.setChoiceId(this.originalItemInfoVo.getStateId());
                fieldValue.setValue(ProjectManager.getInstance(this.originalItemInfoVo.getProjectId()).getWorkflowStateName(this.originalItemInfoVo.getStateId()));
                changedFieldValues.add(fieldValue);
            }
            for (var fieldValue : changedFieldValues) {
                var matchedFieldValue = this.originalItemInfoVo.getFields().stream().filter(field -> field.getFieldId() == fieldValue.getFieldId()).findFirst().orElse(null);
                if( matchedFieldValue != null){
                    matchedFieldValue.setChoiceId(fieldValue.getChoiceId());
                    matchedFieldValue.setValue(fieldValue.getValue());
                }
                else {
                    this.originalItemInfoVo.getFields().add(fieldValue);
                }
            }
        }
        return ruleTypeList;
    }
    private String parseFieldInNotification(Integer projectId, String notificationText) {
        if (notificationText == null || notificationText.isEmpty()) {
            return notificationText;
        }
        ProjectManager helper = ProjectManager.getInstance(projectId);
        // 忽略大小写的正则表达式：匹配 {FieldName_xxx} 和 {FieldValue_xxx}
        Pattern pattern = Pattern.compile("\\{(?i:(FieldName|FieldValue))_(\\d+)\\}");
        Matcher matcher = pattern.matcher(notificationText);

        StringBuilder result = new StringBuilder();

        while (matcher.find()) {
            String fieldType = matcher.group(1); // "FieldName" 或 "FieldValue"
            String strFieldId = matcher.group(2);    // 数字部分
            Integer fieldId = ValueConvert.readInt(strFieldId);
            // 根据不同字段类型构建替换内容
            String replacement = "";
            if ("FieldName".equalsIgnoreCase(fieldType)) {
                replacement = helper.getFieldName(fieldId);
            } else if ("FieldValue".equalsIgnoreCase(fieldType)) {
               var fieldValue = this.originalItemInfoVo.getFields().stream().filter(field -> field.getFieldId() == fieldId).findFirst().orElse(null);
                if( fieldValue != null){
                    if(helper.getFieldType(fieldId) == eFieldTypeDef.RichText.getValue()){
                        replacement = ValueConvert.decodeBase64(fieldValue.getValue());
                        if(replacement == null){
                            replacement = "";
                        }
                    }
                    else {
                        replacement = fieldValue.getValue();
                    }
                }
            } else {
                replacement = ""; // 不匹配的保持为空或按需处理
            }

            matcher.appendReplacement(result, replacement);
        }

        matcher.appendTail(result);

        return result.toString();
    }

}
