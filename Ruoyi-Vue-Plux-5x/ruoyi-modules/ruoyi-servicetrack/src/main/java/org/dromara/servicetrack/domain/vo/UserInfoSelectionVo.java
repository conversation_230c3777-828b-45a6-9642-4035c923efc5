package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.UserInfoSelection;

import java.io.Serializable;

/**
 * 视图对象 user_info_selection
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserInfoSelection.class)
public class UserInfoSelectionVo extends UserInfoFieldVo {
    /**
     * 选择ID
     */
    private Integer choiceId;
}
