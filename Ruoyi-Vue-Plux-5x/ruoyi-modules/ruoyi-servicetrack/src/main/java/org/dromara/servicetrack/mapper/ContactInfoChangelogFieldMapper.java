package org.dromara.servicetrack.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.ContactInfoChangelogField;
import org.dromara.servicetrack.domain.bo.ContactInfoChangelogFieldBo;
import org.dromara.servicetrack.domain.vo.ContactInfoChangelogFieldVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * 联系人信息变更日志字段管理 数据层
 *
 * <AUTHOR>
 */
public interface ContactInfoChangelogFieldMapper extends BaseMapperPlus<ContactInfoChangelogField, ContactInfoChangelogFieldVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ContactInfoChangelogField> buildWrapper(ContactInfoChangelogFieldBo bo) {
        LambdaQueryWrapper<ContactInfoChangelogField> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ContactInfoChangelogField::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, ContactInfoChangelogField::getProjectId, bo.getProjectId());
        lqw.eq(bo.getChangelogId() != null, ContactInfoChangelogField::getChangelogId, bo.getChangelogId());
        lqw.eq(bo.getContactId() != null, ContactInfoChangelogField::getContactId, bo.getContactId());
        lqw.eq(bo.getFieldId() != null, ContactInfoChangelogField::getFieldId, bo.getFieldId());
        return lqw;
    }

    /**
     * 根据项目ID和联系人ID查询变更日志字段列表
     *
     * @param projectId 项目ID
     * @param contactId 联系人ID
     * @return 变更日志字段列表
     */
    List<ContactInfoChangelogFieldVo> selectByProjectAndContact(@Param("projectId") Integer projectId, @Param("contactId") Integer contactId);

    /**
     * 根据变更日志ID查询字段变更详情
     *
     * @param projectId   项目ID
     * @param changelogId 变更日志ID
     * @param contactId   联系人ID
     * @return 字段变更详情列表
     */
    List<ContactInfoChangelogFieldVo> selectByChangelogId(@Param("projectId") Integer projectId, 
                                                         @Param("changelogId") Integer changelogId, 
                                                         @Param("contactId") Integer contactId);

    /**
     * 根据字段ID查询变更历史
     *
     * @param projectId 项目ID
     * @param contactId 联系人ID
     * @param fieldId   字段ID
     * @return 字段变更历史列表
     */
    List<ContactInfoChangelogFieldVo> selectByFieldId(@Param("projectId") Integer projectId, 
                                                     @Param("contactId") Integer contactId, 
                                                     @Param("fieldId") Integer fieldId);
}
