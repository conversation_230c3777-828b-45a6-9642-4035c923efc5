package org.dromara.servicetrack.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class ProjectPageInfoVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * Page ID
     */
    private Integer pageId;
    /**
     * Page Name
     */
    private String pageName;
    /**
     * module id
     * */
    private Integer moduleId;

    public ProjectPageInfoVo(Integer pageId, String pageName, Integer moduleId) {
        this.pageId = pageId;
        this.pageName = pageName;
        this.moduleId = moduleId;
    }
}
