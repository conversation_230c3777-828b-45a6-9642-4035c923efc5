package org.dromara.servicetrack.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 文件夹树节点视图对象
 *
 * <AUTHOR>
 */
@Data
public class FolderTreeNodeVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文件夹ID
     */
    private Integer folderId;

    /**
     * 父文件夹ID (0表示根节点)
     */
    private Integer parentId;

    /**
     * 文件夹名称
     */
    private String folderName;

    /**
     * 显示顺序
     */
    private Integer displayOrder;

    /**
     * 项目ID
     */
    private Integer projectId;
}
