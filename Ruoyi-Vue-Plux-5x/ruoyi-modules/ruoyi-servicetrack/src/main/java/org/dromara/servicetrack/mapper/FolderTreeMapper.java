package org.dromara.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.servicetrack.domain.FolderTree;
import org.dromara.servicetrack.domain.bo.FolderTreeBo;
import org.dromara.servicetrack.domain.vo.FolderTreeVo;
import org.dromara.servicetrack.domain.vo.FolderTreeNodeVo;

import java.util.List;

/**
 * 文件夹树结构管理 数据层
 *
 * <AUTHOR>
 */
public interface FolderTreeMapper extends BaseMapperPlus<FolderTree, FolderTreeVo> {
    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<FolderTree> buildWrapper(FolderTreeBo bo) {
        LambdaQueryWrapper<FolderTree> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, FolderTree::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, FolderTree::getProjectId, bo.getProjectId());
        lqw.eq(bo.getParentFolder() != null, FolderTree::getParentFolder, bo.getParentFolder());
        lqw.eq(bo.getChildFolder() != null, FolderTree::getChildFolder, bo.getChildFolder());
        lqw.eq(bo.getDisplayOrder() != null, FolderTree::getDisplayOrder, bo.getDisplayOrder());
        lqw.orderByAsc(FolderTree::getDisplayOrder);

        return lqw;
    }

    /**
     * 根据项目ID获取文件夹树结构数据
     *
     * @param projectId 项目ID
     * @return 文件夹树结构数据列表
     */
    List<FolderTreeNodeVo> selectFolderTreeByProjectId(@Param("projectId") Integer projectId);

    /**
     * 根据项目ID和根文件夹ID获取文件夹树结构数据
     *
     * @param projectId 项目ID
     * @param rootId 根文件夹ID，如果为null则获取所有树结构
     * @return 文件夹树结构数据列表
     */
    List<FolderTreeNodeVo> selectFolderTreeByProjectIdAndRootId(@Param("projectId") Integer projectId, @Param("rootId") Integer rootId);

    /**
     * 根据项目ID和父文件夹ID获取子文件夹列表（包含文件夹名称）
     *
     * @param projectId 项目ID
     * @param parentFolder 父文件夹ID
     * @return 子文件夹列表
     */
    List<FolderTreeVo> selectChildFoldersWithNames(@Param("projectId") Integer projectId, @Param("parentFolder") Integer parentFolder);
}
