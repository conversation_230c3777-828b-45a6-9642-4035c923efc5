package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemSelection;

import java.util.Date;

/**
 *视图对象 item_selection
 */
@Data
@EqualsAndHashCode(callSuper =true)
@AutoMapper(target = ItemSelection.class)
public class ItemSelectionVo extends ItemFieldVo{
    /**
     * choice id
     */
    private Integer choiceId;
}
