package org.dromara.servicetrack.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.servicetrack.domain.vo.ItemAttachmentVo;
import org.dromara.common.core.utils.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.Map;

@Data
public abstract class CustomFieldsVo implements Serializable {
    // 自定义字段先用String接收
    @JsonIgnore
    private String datetimeFieldsJson;
    @JsonIgnore
    private String textFieldsJson;
    @JsonIgnore
    private String selectionFieldsJson;
    @JsonIgnore
    private String amountFieldsJson;

    // 转换后的字段（不参与数据库映射）
    @JsonIgnore
    private Map<String, Date> datetimeFields;
    @JsonIgnore
    protected Map<String, String> textFields;
    @JsonIgnore
    private Map<String, List<Integer>> selectionFields;

    @JsonIgnore
    private Map<String, Double> amountFields;

    // 提供转换方法
    public void parseCustomFields() {
        try {
            // 处理 datetimeFieldsJson
            if (StringUtils.isNotEmpty(datetimeFieldsJson)) {
                // 如果 datetimeFieldsJson 不是以 { 开头，则手动添加 {}
                String json = datetimeFieldsJson.trim();
                if (!json.startsWith("{")) {
                    json = "{" + json + "}";
                }
                // 转义特殊字符
                json = StringUtils.escapeJsonSpecialChars(json);
                datetimeFields = JsonUtils.parseObject(json, new TypeReference<Map<String, Date>>() {});
            } else {
                datetimeFields = new HashMap<>();  // 提供默认空map而不是null
            }

            // 处理 textFieldsJson
            if (StringUtils.isNotEmpty(textFieldsJson)) {
                String json = textFieldsJson.trim();
                if (!json.startsWith("{")) {
                    json = "{" + json + "}";
                }
                // 转义特殊字符
                json = StringUtils.escapeJsonSpecialChars(json);
                textFields = JsonUtils.parseObject(json, new TypeReference<Map<String, String>>() {});
            } else {
                textFields = new HashMap<>();
            }
            // 处理 amountFieldsJson
            if (StringUtils.isNotEmpty(amountFieldsJson)) {
                String json = amountFieldsJson.trim();
                if (!json.startsWith("{")) {
                    json = "{" + json + "}";
                }
                // 转义特殊字符
                json = StringUtils.escapeJsonSpecialChars(json);
                amountFields = JsonUtils.parseObject(json, new TypeReference<Map<String, Double>>() {});
            } else {
                amountFields = new HashMap<>();
            }

            // 处理 selectionFieldsJson
            if (StringUtils.isNotEmpty(selectionFieldsJson)) {
                String json = selectionFieldsJson.trim();
                if (!json.startsWith("{")) {
                    json = "{" + json + "}";
                }
                // 转义特殊字符
                json = StringUtils.escapeJsonSpecialChars(json);
                selectionFields = JsonUtils.parseObject(json, new TypeReference<Map<String, List<Integer>>>() {});
            } else {
                selectionFields = new HashMap<>();
            }
        } catch (ServiceException e) {
            throw new RuntimeException("Failed to parse custom fields", e);
        }
    }

    @JsonIgnore
    private List<ItemAttachmentVo> attachments;
    @JsonIgnore
    private List<ItemTimestampVo> timestamps;
    public String getAttachmentJsonValue() {
        return JsonUtils.toJsonString(attachments);
    }

}
