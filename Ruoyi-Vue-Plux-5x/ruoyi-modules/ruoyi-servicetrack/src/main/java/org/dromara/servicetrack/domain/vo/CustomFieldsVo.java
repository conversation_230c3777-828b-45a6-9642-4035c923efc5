package org.dromara.servicetrack.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.servicetrack.domain.vo.ItemAttachmentVo;
import org.dromara.common.core.utils.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.Map;

@Data
public abstract class CustomFieldsVo implements Serializable {
    // 自定义字段先用String接收
    @JsonIgnore
    private String datetimeFieldsJson;
    @JsonIgnore
    private String textFieldsJson;
    @JsonIgnore
    private String selectionFieldsJson;
    @JsonIgnore
    private String amountFieldsJson;

    // 转换后的字段（不参与数据库映射）
    @JsonIgnore
    private Map<String, Date> datetimeFields;
    @JsonIgnore
    protected Map<String, String> textFields;
    @JsonIgnore
    private Map<String, List<Integer>> selectionFields;

    @JsonIgnore
    private Map<String, Double> amountFields;

    // 提供转换方法
    public void parseCustomFields() {
        try {
            // 处理 datetimeFieldsJson
            if (StringUtils.isNotEmpty(datetimeFieldsJson)) {
                try {
                    String json = prepareJsonString(datetimeFieldsJson);
                    datetimeFields = JsonUtils.parseObject(json, new TypeReference<Map<String, Date>>() {});
                } catch (Exception e) {
                    throw new RuntimeException("Failed to parse datetimeFieldsJson: " + datetimeFieldsJson, e);
                }
            } else {
                datetimeFields = new HashMap<>();  // 提供默认空map而不是null
            }

            // 处理 textFieldsJson
            if (StringUtils.isNotEmpty(textFieldsJson)) {
                try {
                    String json = prepareJsonString(textFieldsJson);
                    textFields = JsonUtils.parseObject(json, new TypeReference<Map<String, String>>() {});
                } catch (Exception e) {
                    throw new RuntimeException("Failed to parse textFieldsJson: " + textFieldsJson, e);
                }
            } else {
                textFields = new HashMap<>();
            }

            // 处理 amountFieldsJson
            if (StringUtils.isNotEmpty(amountFieldsJson)) {
                try {
                    String json = prepareJsonString(amountFieldsJson);
                    amountFields = JsonUtils.parseObject(json, new TypeReference<Map<String, Double>>() {});
                } catch (Exception e) {
                    throw new RuntimeException("Failed to parse amountFieldsJson: " + amountFieldsJson, e);
                }
            } else {
                amountFields = new HashMap<>();
            }

            // 处理 selectionFieldsJson
            if (StringUtils.isNotEmpty(selectionFieldsJson)) {
                try {
                    String json = prepareJsonString(selectionFieldsJson);
                    selectionFields = JsonUtils.parseObject(json, new TypeReference<Map<String, List<Integer>>>() {});
                } catch (Exception e) {
                    throw new RuntimeException("Failed to parse selectionFieldsJson: " + selectionFieldsJson, e);
                }
            } else {
                selectionFields = new HashMap<>();
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse custom fields", e);
        }
    }

    private String prepareJsonString(String jsonString) {
        String json = jsonString.trim();
        if (!json.startsWith("{")) {
            json = "{" + json + "}";
        }
        // 转义特殊字符
        return StringUtils.escapeJsonSpecialChars(json);
    }

    @JsonIgnore
    private List<ItemAttachmentVo> attachments;
    @JsonIgnore
    private List<ItemTimestampVo> timestamps;
    public String getAttachmentJsonValue() {
        return JsonUtils.toJsonString(attachments);
    }

}
