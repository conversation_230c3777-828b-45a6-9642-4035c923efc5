package org.dromara.servicetrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.servicetrack.domain.SystemSetting;
import org.dromara.servicetrack.domain.bo.ProjectSettingBinderBo;
import org.dromara.servicetrack.domain.bo.SystemSettingBinderBo;
import org.dromara.servicetrack.domain.vo.SystemSettingVo;
import org.dromara.servicetrack.mapper.SystemSettingMapper;
import org.dromara.servicetrack.service.ISystemSettingService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 系统设置 服务层实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SystemSettingServiceImpl implements ISystemSettingService {
    private final SystemSettingMapper systemSettingMapper;
    @Override
    public List<SystemSettingVo> selectSystemSettingList(List<Integer> settingIds) {
        if(settingIds == null || settingIds.isEmpty()){
            throw new ServiceException("settingIds is null or empty");
        }
        return systemSettingMapper.selectBySettingIds(settingIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(SystemSettingBinderBo bo) {
        if(bo == null){
            throw new ServiceException("SystemSettingBinderBo is null");
        }
        if (bo.getSettingList() == null || bo.getSettingList().isEmpty()) {
            throw new ServiceException("settingList is null or empty");
        }
        var settingIds = bo.getSettingList().stream().map(ProjectSettingBinderBo.SettingInfoBo::getSettingId).toList();
        var settingListVo = selectSystemSettingList(settingIds);

        List<SystemSetting> insertList = new ArrayList<>();
        List<SystemSetting> updateList = new ArrayList<>();
        for (var setting : bo.getSettingList()) {
            var settingId = setting.getSettingId();
            if (settingListVo.stream().noneMatch(s -> s.getSettingId().equals(settingId))) {
                //need to insert
                var systemSetting = bo.convertToSystemSetting(setting, null);
                insertList.add(systemSetting);
            }
            else{
                //need to update
                var settingVo = settingListVo.stream().filter(s -> s.getSettingId().equals(settingId)).findFirst();
                if (settingVo.isPresent()) {
                    var systemSetting = bo.convertToSystemSetting(setting, settingVo.get().getId());
                    updateList.add(systemSetting);
                }
            }
        }
        if (!insertList.isEmpty()) {
            systemSettingMapper.insertBatch(insertList);
        }
        if (!updateList.isEmpty()) {
            systemSettingMapper.updateBatchById(updateList);
        }
        return true;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Integer> settingIds, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否允许删除
        }
        if( settingIds == null || settingIds.isEmpty()){
            return false;
        }
        return systemSettingMapper.delete(new LambdaQueryWrapper<SystemSetting>().in(SystemSetting::getSettingId, settingIds)) > 0;
    }
}
