package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.*;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
/**
 *  Item_Selection
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("item_selection")
public class ItemSelection extends ItemField {

    /**
     * choice id
     */
    @TableField(value = "choice_id")
    private Integer choiceId;
}
