package org.dromara.servicetrack.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.servicetrack.constant.eCultureCode;

import java.util.Date;

/**
 * changelog 基础视图对象 base changelog field
 */
@Data
@EqualsAndHashCode(callSuper =true)
public class BaseChangelogFieldVo extends BaseFieldVo{

    /**
     * changelog Id
     */
    private Integer changelogId;
    /**
     * change_from
     */
    private String changeFrom;

    /**
     * change_to
     */
    private String changeTo;

    /**
     * change_from_html
     */
    private String changeFromHtml;

    /**
     * change_to_html
     */
    private String changeToHtml;

    /**
     * change_from_plain_text
     */
    private String changeFromPlainText;

    /**
     * change_to_plain_text
     */
    private String changeToPlainText;

    /**
     * description
     */
    private String description;

    @JsonIgnore
    private Date logTime;

    @JsonIgnore
    private Integer changedById;

    @JsonIgnore
    private Long avatar;

    public void parseDescription4MultiLang(eCultureCode langId){

        if( langId == eCultureCode.ZH_CN)
        {
            this.parseDescription2Chinese();
        }
    }
    private void parseDescription2Chinese()
    {
//        if( StringUtils.isEmpty(this.description))
//            return;
//        this.description = this.description.replaceAll("changed", "更改");
//        this.description = this.description.replaceAll("from", "从");
//        this.description = this.description.replaceAll("to", "到");
    }
}
