package org.dromara.servicetrack.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
/**
 *  sys_user simple info
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class UserInfo extends BaseEntity {

    @TableId(value = "user_id")
    private Long userId;

    /**
     * user name
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * nick name
     */
    @TableField(value = "nick_name")
    private String nickName;

    /**
     * external user id
     */
    @TableField(value = "external_user_id")
    private Integer externalUserId;

    /**
     * service track 用户类型(refer to eSTModuleType): 1:EP, 2:SP, 3: EP & SP
     */
    @TableField(value = "st_user_type")
    private Integer stUserType;

    /**
     * status
     */
    private Integer status;

    @TableField(value = "dept_id")
    private Long deptId;
    /**
     * 用户邮箱
     */
    private String email;

    private String sex;

    /**
     * 创建者
     */
    @TableField(value = "create_by")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 手机号码
     */
    @TableField(value = "phonenumber")
    private String phoneNumber;

    /**
     * 是否为service track管理员
     */
    @TableField(value="st_admin")
    private Integer stAdmin;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
