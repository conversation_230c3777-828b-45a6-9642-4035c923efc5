package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemTempInfo;

@Data
@EqualsAndHashCode(callSuper=true)
@AutoMapper(target = ItemTempInfo.class)
public class ItemTempInfoListVo extends ItemTempInfoVo{
    /**
     * group id
     */
    private Integer groupId;
    /**
     * display order
     */
    private Integer displayOrder;

    /**
     * item type
     */
    private Integer itemType;
}
