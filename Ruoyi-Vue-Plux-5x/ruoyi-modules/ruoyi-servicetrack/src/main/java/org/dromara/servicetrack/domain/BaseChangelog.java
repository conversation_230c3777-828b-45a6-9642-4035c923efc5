package org.dromara.servicetrack.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.Date;
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BaseChangelog extends STBaseEntity {
    /**
     * ID
     */
    @TableId(value = "key_id")
    private Long id;

    /**
     * project id
     */
    @TableField(value = "project_id")
    private Integer projectId;

    /**
     * changelog Id
     */
    @TableField(value = "changelog_id")
    private Integer changelogId;

    /**
     * log_time
     */
    @TableField(value = "log_time")
    private Date logTime;

    /**
     * createdBy_id
     */
    @TableField(value = "changedby_id")
    private Integer changedById;


}
