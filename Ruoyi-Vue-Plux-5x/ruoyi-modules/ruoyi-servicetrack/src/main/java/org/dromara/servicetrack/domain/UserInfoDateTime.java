package org.dromara.servicetrack.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *  user_info_DateTime
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("user_info_datetime")
public class UserInfoDateTime extends UserInfoField{
    /**
     * date time
     */
    @TableField(value = "datetime")
    private Date dateTime;
}
