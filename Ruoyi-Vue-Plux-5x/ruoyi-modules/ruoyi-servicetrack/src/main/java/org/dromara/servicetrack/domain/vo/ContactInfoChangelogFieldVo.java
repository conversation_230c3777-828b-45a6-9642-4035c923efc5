package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ContactInfoChangelogField;

/**
 * 联系人信息变更日志字段视图对象 contact_info_changelog_field
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ContactInfoChangelogField.class)
public class ContactInfoChangelogFieldVo extends BaseChangelogFieldVo {
    /**
     * 联系人ID
     */
    private Integer contactId;

    /**
     * 联系人名称（扩展字段，用于显示）
     */
    private String contactName;
}
