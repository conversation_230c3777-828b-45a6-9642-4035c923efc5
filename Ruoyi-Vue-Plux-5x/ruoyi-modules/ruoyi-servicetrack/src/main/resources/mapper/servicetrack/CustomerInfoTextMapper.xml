<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.CustomerInfoTextMapper">

    <resultMap type="org.dromara.servicetrack.domain.vo.CustomerInfoTextVo" id="CustomerInfoTextResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="fieldId" column="field_id"/>
        <result property="text" column="text"/>
        <result property="customerName" column="customer_name"/>
        <result property="fieldName" column="field_name"/>
        <result property="fieldType" column="field_type"/>
    </resultMap>

    <sql id="selectCustomerInfoTextVo">
        select cit.key_id, cit.project_id, cit.customer_id, cit.field_id, cit.text,
               ci.customer_name,
               pf.field_name, pf.field_type
        from customer_info_text cit
        left join customer_info ci on ci.project_id = cit.project_id and ci.customer_id = cit.customer_id
        left join project_field pf on pf.project_id = cit.project_id and pf.field_id = cit.field_id
    </sql>

    <select id="selectByProjectAndCustomer" resultMap="CustomerInfoTextResult">
        <include refid="selectCustomerInfoTextVo"/>
        where cit.project_id = #{projectId} and cit.customer_id = #{customerId}
        order by cit.field_id
    </select>

    <select id="selectByProjectId" resultMap="CustomerInfoTextResult">
        <include refid="selectCustomerInfoTextVo"/>
        where cit.project_id = #{projectId}
        order by cit.customer_id, cit.field_id
    </select>

    <select id="selectByFieldId" resultMap="CustomerInfoTextResult">
        <include refid="selectCustomerInfoTextVo"/>
        where cit.project_id = #{projectId} 
        and cit.customer_id = #{customerId} 
        and cit.field_id = #{fieldId}
    </select>

</mapper>
