<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.FolderInfoMapper">

    <resultMap type="org.dromara.servicetrack.domain.vo.FolderInfoVo" id="FolderInfoResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="folderId" column="folder_id"/>
        <result property="folderName" column="folder_name"/>
        <result property="folderType" column="folder_type"/>
        <result property="folderDescription" column="folder_description"/>
        <result property="createdTime" column="created_time"/>
        <result property="createdBy" column="created_by"/>
    </resultMap>

    <sql id="selectFolderInfoVo">
        select key_id, project_id, folder_id, folder_name, folder_type, 
               folder_description, created_time, created_by
        from folder_info
    </sql>

    <select id="selectList" resultMap="FolderInfoResult">
        <include refid="selectFolderInfoVo"/>
        <where>
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != '' and ew.nonEmptyOfNormal">
                and ${ew.sqlSegment}
            </if>
        </where>
    </select>

</mapper>
