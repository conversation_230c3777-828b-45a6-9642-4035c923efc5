<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.ContactInfoChangelogFieldMapper">

    <resultMap type="org.dromara.servicetrack.domain.vo.ContactInfoChangelogFieldVo" id="ContactInfoChangelogFieldResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="changelogId" column="changelog_id"/>
        <result property="contactId" column="contact_id"/>
        <result property="fieldId" column="field_id"/>
        <result property="changeFrom" column="change_from"/>
        <result property="changeTo" column="change_to"/>
        <result property="description" column="description"/>
        <result property="contactName" column="contact_name"/>
        <result property="logTime" column="log_time"/>
        <result property="changedById" column="changedby_id"/>
        <result property="fieldName" column="field_name"/>
    </resultMap>

    <sql id="selectContactInfoChangelogFieldVo">
        select cicf.key_id, cicf.project_id, cicf.changelog_id, cicf.contact_id, 
               cicf.field_id, cicf.change_from, cicf.change_to, cicf.description,
               ci.contact_name,
               cic.log_time, cic.changedby_id,
               pf.field_name
        from contact_info_changelog_field cicf
        left join contact_info ci on ci.project_id = cicf.project_id and ci.contact_id = cicf.contact_id
        left join contact_info_changelog cic on cic.project_id = cicf.project_id 
                                              and cic.changelog_id = cicf.changelog_id 
                                              and cic.contact_id = cicf.contact_id
        left join project_field pf on pf.project_id = cicf.project_id and pf.field_id = cicf.field_id
    </sql>

    <select id="selectByProjectAndContact" resultMap="ContactInfoChangelogFieldResult">
        <include refid="selectContactInfoChangelogFieldVo"/>
        where cicf.project_id = #{projectId} and cicf.contact_id = #{contactId}
        order by cic.log_time desc, cicf.changelog_id desc, cicf.field_id
    </select>

    <select id="selectByChangelogId" resultMap="ContactInfoChangelogFieldResult">
        <include refid="selectContactInfoChangelogFieldVo"/>
        where cicf.project_id = #{projectId} 
        and cicf.changelog_id = #{changelogId} 
        and cicf.contact_id = #{contactId}
        order by cicf.field_id
    </select>

    <select id="selectByFieldId" resultMap="ContactInfoChangelogFieldResult">
        <include refid="selectContactInfoChangelogFieldVo"/>
        where cicf.project_id = #{projectId} 
        and cicf.contact_id = #{contactId} 
        and cicf.field_id = #{fieldId}
        order by cic.log_time desc, cicf.changelog_id desc
    </select>

</mapper>
