<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.ContactInfoChangelogTextMapper">

    <resultMap type="org.dromara.servicetrack.domain.vo.ContactInfoChangelogTextVo" id="ContactInfoChangelogTextResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="changelogId" column="changelog_id"/>
        <result property="contactId" column="contact_id"/>
        <result property="fieldId" column="field_id"/>
        <result property="changeFrom" column="change_from"/>
        <result property="changeTo" column="change_to"/>
        <result property="contactName" column="contact_name"/>
        <result property="fieldName" column="field_name"/>
        <result property="fieldType" column="field_type"/>
    </resultMap>

    <sql id="selectContactInfoChangelogTextVo">
        select cict.key_id, cict.project_id, cict.changelog_id, cict.contact_id, 
               cict.field_id, cict.change_from, cict.change_to,
               ci.contact_name,
               pf.field_name, pf.field_type
        from contact_info_changelog_text cict
        left join contact_info ci on ci.project_id = cict.project_id and ci.contact_id = cict.contact_id
        left join project_field pf on pf.project_id = cict.project_id and pf.field_id = cict.field_id
    </sql>

    <select id="selectByProjectAndContact" resultMap="ContactInfoChangelogTextResult">
        <include refid="selectContactInfoChangelogTextVo"/>
        where cict.project_id = #{projectId} and cict.contact_id = #{contactId}
        order by cict.changelog_id desc, cict.field_id
    </select>

    <select id="selectByChangelogId" resultMap="ContactInfoChangelogTextResult">
        <include refid="selectContactInfoChangelogTextVo"/>
        where cict.project_id = #{projectId} 
        and cict.changelog_id = #{changelogId} 
        and cict.contact_id = #{contactId}
        order by cict.field_id
    </select>

    <select id="selectByFieldId" resultMap="ContactInfoChangelogTextResult">
        <include refid="selectContactInfoChangelogTextVo"/>
        where cict.project_id = #{projectId} 
        and cict.contact_id = #{contactId} 
        and cict.field_id = #{fieldId}
        order by cict.changelog_id desc
    </select>

</mapper>
