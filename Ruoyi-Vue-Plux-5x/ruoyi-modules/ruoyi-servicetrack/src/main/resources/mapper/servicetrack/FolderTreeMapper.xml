<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.FolderTreeMapper">

    <resultMap type="org.dromara.servicetrack.domain.vo.FolderTreeVo" id="FolderTreeResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="parentFolder" column="parent_folder"/>
        <result property="childFolder" column="child_folder"/>
        <result property="displayOrder" column="display_order"/>
    </resultMap>

    <resultMap type="org.dromara.servicetrack.domain.vo.FolderTreeVo" id="FolderTreeWithNamesResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="parentFolder" column="parent_folder"/>
        <result property="parentFolderName" column="parent_folder_name"/>
        <result property="childFolder" column="child_folder"/>
        <result property="childFolderType" column="child_folder_type"/>
        <result property="childFolderName" column="child_folder_name"/>
        <result property="displayOrder" column="display_order"/>
    </resultMap>

    <sql id="selectFolderTreeVo">
        select key_id, project_id, parent_folder, child_folder, display_order
        from folder_tree
    </sql>

    <sql id="selectFolderTreeWithNamesVo">
        select ft.key_id, ft.project_id, ft.parent_folder, ft.child_folder, ft.display_order,
               pf.folder_name as parent_folder_name,
               cf.folder_name as child_folder_name
        from folder_tree ft
        left join folder_info pf on ft.project_id = pf.project_id and ft.parent_folder = pf.folder_id
        left join folder_info cf on ft.project_id = cf.project_id and ft.child_folder = cf.folder_id
    </sql>

    <select id="selectList" resultMap="FolderTreeResult">
        <include refid="selectFolderTreeVo"/>
        <where>
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != '' and ew.nonEmptyOfNormal">
                and ${ew.sqlSegment}
            </if>
        </where>
    </select>

    <select id="selectListWithNames" resultMap="FolderTreeWithNamesResult">
        <include refid="selectFolderTreeWithNamesVo"/>
        <where>
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != '' and ew.nonEmptyOfNormal">
                and ${ew.sqlSegment}
            </if>
        </where>
        order by ft.display_order
    </select>

    <resultMap type="org.dromara.servicetrack.domain.vo.FolderTreeNodeVo" id="FolderTreeNodeResult">
        <result property="folderId" column="folder_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="folderName" column="folder_name"/>
        <result property="displayOrder" column="display_order"/>
        <result property="projectId" column="project_id"/>
    </resultMap>

    <select id="selectFolderTreeByProjectId" resultMap="FolderTreeNodeResult">
        WITH RECURSIVE folder_hierarchy AS (
            -- 查找所有根文件夹（没有作为子文件夹出现在folder_tree中的文件夹）
            SELECT
                fi.folder_id,
                0 as parent_id,
                fi.folder_name,
                0 as display_order,
                fi.project_id
            FROM folder_info fi
            WHERE fi.project_id = #{projectId}
              AND fi.folder_id NOT IN (
                  SELECT DISTINCT ft.child_folder
                  FROM folder_tree ft
                  WHERE ft.project_id = #{projectId}
              )

            UNION ALL

            -- 递归查找子文件夹
            SELECT
                fi.folder_id,
                ft.parent_folder as parent_id,
                fi.folder_name,
                COALESCE(ft.display_order, 0) as display_order,
                fi.project_id
            FROM folder_tree ft
            INNER JOIN folder_info fi ON ft.child_folder = fi.folder_id AND ft.project_id = fi.project_id
            INNER JOIN folder_hierarchy fh ON ft.parent_folder = fh.folder_id
            WHERE ft.project_id = #{projectId}
        )
        SELECT
            folder_id,
            parent_id,
            folder_name,
            display_order,
            project_id
        FROM folder_hierarchy
        ORDER BY parent_id, display_order, folder_id
    </select>

    <select id="selectFolderTreeByProjectIdAndRootId" resultMap="FolderTreeNodeResult">
        <choose>
            <when test="rootId != null">
                -- 当指定rootId时，获取rootId节点及其完整子树（支持多层嵌套）
                WITH RECURSIVE folder_hierarchy AS (
                    -- 起始节点：rootId本身作为根节点
                    SELECT
                        fi.folder_id,
                        0 as parent_id,
                        fi.folder_name,
                        0 as display_order,
                        fi.project_id,
                        0 as level,
                        CAST(fi.folder_id AS VARCHAR(1000)) as path
                    FROM folder_info fi
                    WHERE fi.project_id = #{projectId}
                      AND fi.folder_id = #{rootId}

                    UNION ALL

                    -- 递归查找子文件夹（支持多层嵌套）
                    SELECT
                        fi.folder_id,
                        ft.parent_folder as parent_id,
                        fi.folder_name,
                        COALESCE(ft.display_order, 0) as display_order,
                        fi.project_id,
                        fh.level + 1 as level,
                        CONCAT(fh.path, '->', fi.folder_id) as path
                    FROM folder_tree ft
                    INNER JOIN folder_info fi ON ft.child_folder = fi.folder_id AND ft.project_id = fi.project_id
                    INNER JOIN folder_hierarchy fh ON ft.parent_folder = fh.folder_id
                    WHERE ft.project_id = #{projectId}
                      AND level &lt; 10  -- 防止无限递归，最大支持10层
                      AND POSITION(CONCAT('->', fi.folder_id, '->') IN CONCAT('->', fh.path, '->')) = 0  -- 防止循环引用
                )
                SELECT
                    folder_id,
                    CASE
                        WHEN level = 0 THEN 0
                        ELSE parent_id
                    END as parent_id,
                    folder_name,
                    display_order,
                    project_id
                FROM folder_hierarchy
                ORDER BY level, parent_id, display_order, folder_id
            </when>
            <otherwise>
                -- 当rootId为null时，获取所有树结构（支持多层嵌套）
                WITH RECURSIVE folder_hierarchy AS (
                    -- 查找所有根文件夹（没有作为子文件夹出现在folder_tree中的文件夹）
                    SELECT
                        fi.folder_id,
                        0 as parent_id,
                        fi.folder_name,
                        0 as display_order,
                        fi.project_id,
                        0 as level,
                        CAST(fi.folder_id AS VARCHAR(1000)) as path
                    FROM folder_info fi
                    WHERE fi.project_id = #{projectId}
                      AND fi.folder_id NOT IN (
                          SELECT DISTINCT ft.child_folder
                          FROM folder_tree ft
                          WHERE ft.project_id = #{projectId}
                      )

                    UNION ALL

                    -- 递归查找子文件夹（支持多层嵌套）
                    SELECT
                        fi.folder_id,
                        ft.parent_folder as parent_id,
                        fi.folder_name,
                        COALESCE(ft.display_order, 0) as display_order,
                        fi.project_id,
                        fh.level + 1 as level,
                        CONCAT(fh.path, '->', fi.folder_id) as path
                    FROM folder_tree ft
                    INNER JOIN folder_info fi ON ft.child_folder = fi.folder_id AND ft.project_id = fi.project_id
                    INNER JOIN folder_hierarchy fh ON ft.parent_folder = fh.folder_id
                    WHERE ft.project_id = #{projectId}
                      AND level &lt;  10  -- 防止无限递归，最大支持10层
                      AND POSITION(CONCAT('->', fi.folder_id, '->') IN CONCAT('->', fh.path, '->')) = 0  -- 防止循环引用
                )
                SELECT
                    folder_id,
                    parent_id,
                    folder_name,
                    display_order,
                    project_id
                FROM folder_hierarchy
                ORDER BY level, parent_id, display_order, folder_id
            </otherwise>
        </choose>
    </select>

    <select id="selectChildFoldersWithNames" resultMap="FolderTreeWithNamesResult">
                <!-- 查找folder_tree表中的直接子文件夹 -->
                SELECT
                    cf.key_id,
                    ft.project_id,
                    ft.parent_folder,
                    pf.folder_name as parent_folder_name,
                    ft.child_folder,
                    cf.folder_name as child_folder_name,
                    cf.folder_type as child_folder_type,
                    ft.display_order
                FROM folder_tree ft
                LEFT JOIN folder_info pf ON ft.project_id = pf.project_id AND ft.parent_folder = pf.folder_id
                INNER JOIN folder_info cf ON ft.project_id = cf.project_id AND ft.child_folder = cf.folder_id
                WHERE ft.project_id = #{projectId}
                  AND ft.parent_folder = #{parentFolder}
                ORDER BY ft.display_order, ft.child_folder

    </select>

</mapper>
