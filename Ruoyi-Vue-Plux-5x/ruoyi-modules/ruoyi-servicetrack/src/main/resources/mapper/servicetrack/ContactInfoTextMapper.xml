<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.ContactInfoTextMapper">

    <resultMap type="org.dromara.servicetrack.domain.vo.ContactInfoTextVo" id="ContactInfoTextResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="contactId" column="contact_id"/>
        <result property="fieldId" column="field_id"/>
        <result property="text" column="text"/>
        <result property="contactName" column="contact_name"/>
        <result property="fieldName" column="field_name"/>
        <result property="fieldType" column="field_type"/>
    </resultMap>

    <sql id="selectContactInfoTextVo">
        select cit.key_id, cit.project_id, cit.contact_id, cit.field_id, cit.text,
               ci.contact_name,
               pf.field_name, pf.field_type
        from contact_info_text cit
        left join contact_info ci on ci.project_id = cit.project_id and ci.contact_id = cit.contact_id
        left join project_field pf on pf.project_id = cit.project_id and pf.field_id = cit.field_id
    </sql>

    <select id="selectByProjectAndContact" resultMap="ContactInfoTextResult">
        <include refid="selectContactInfoTextVo"/>
        where cit.project_id = #{projectId} and cit.contact_id = #{contactId}
        order by cit.field_id
    </select>

    <select id="selectByProjectId" resultMap="ContactInfoTextResult">
        <include refid="selectContactInfoTextVo"/>
        where cit.project_id = #{projectId}
        order by cit.contact_id, cit.field_id
    </select>

    <select id="selectByFieldId" resultMap="ContactInfoTextResult">
        <include refid="selectContactInfoTextVo"/>
        where cit.project_id = #{projectId} 
        and cit.contact_id = #{contactId} 
        and cit.field_id = #{fieldId}
    </select>

</mapper>
