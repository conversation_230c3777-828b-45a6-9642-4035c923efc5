<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.CustomerInfoMapper">

    <resultMap type="org.dromara.servicetrack.domain.vo.CustomerInfoVo" id="CustomerInfoResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerName" column="customer_name"/>
        <result property="customerStatus" column="customer_status"/>
        <result property="customerNotes" column="customer_notes"/>
        <result property="createdTime" column="created_time"/>
        <result property="modifiedTime" column="modified_time"/>

        <result property="textFieldsJson" column="text_fields"/>
        <result property="datetimeFieldsJson" column="datetime_fields"/>
        <result property="selectionFieldsJson" column="selection_fields"/>
    </resultMap>
    <resultMap type="org.dromara.servicetrack.domain.vo.CustomerInfoListVo" id="CustomerInfoListResult">
        <id property="id" column="key_id"/>
        <result property="textFieldsJson" column="text_fields"/>
        <result property="datetimeFieldsJson" column="datetime_fields"/>
        <result property="selectionFieldsJson" column="selection_fields"/>
    </resultMap>
    <select id="selectPageUserList" resultMap="CustomerInfoListResult">
        <if test="(textFieldIds != null and textFieldIds.size() > 0) || (dateTimeFieldIds != null and dateTimeFieldIds.size() > 0) || (selectionFieldIds != null and selectionFieldIds.size() > 0)">
            WITH
            <trim suffixOverrides=",">
                <if test="textFieldIds != null and textFieldIds.size() > 0">
                    text_fields AS (
                    SELECT project_id, customer_id,
                    GROUP_CONCAT(
                    CONCAT('"', field_id, '":"', REPLACE(text, '"', '\\"'), '"')
                    SEPARATOR ','
                    ) AS text_values,
                    MIN(text) AS min_text -- 添加一列用于排序
                    FROM customer_info_text
                    WHERE project_id = #{projectId}
                    AND
                    customer_id IN (SELECT customer_id FROM customer_info u WHERE project_id = #{projectId}
                    <if test="customerSqlSegment != null and customerSqlSegment != ''">
                        and ${customerSqlSegment}
                    </if> )
                    AND
                    field_id IN
                    <foreach item="fieldId" collection="textFieldIds" open="(" separator="," close=")">
                        #{fieldId}
                    </foreach>
                    GROUP BY project_id, customer_id
                    ),
                </if>
                <if test="dateTimeFieldIds != null and dateTimeFieldIds.size() > 0">

                    datetime_fields AS (
                    SELECT project_id, customer_id,
                    GROUP_CONCAT(
                    CONCAT('"', field_id, '":"', datetime, '"')
                    SEPARATOR ','
                    ) AS datetime_values,
                    MIN(datetime) AS min_datetime -- 添加一列用于排序
                    FROM customer_info_datetime
                    WHERE  project_id = #{projectId}
                    AND
                    customer_id IN (SELECT customer_id FROM customer_info u WHERE project_id = #{projectId}
                    <if test="customerSqlSegment != null and customerSqlSegment != ''">
                        and ${customerSqlSegment}
                    </if> )
                    AND  field_id IN
                    <foreach item="fieldId" collection="dateTimeFieldIds" open="(" separator="," close=")">
                        #{fieldId}
                    </foreach>
                    GROUP BY project_id, customer_id
                    ),
                </if>
                <if test="selectionFieldIds != null and selectionFieldIds.size() > 0">
                    selection_fields AS (
                    SELECT project_id, customer_id, field_id,
                    CONCAT('[', GROUP_CONCAT(choice_id ORDER BY choice_id), ']') AS choices,
                    MIN(choice_id) as min_choice_id
                    FROM customer_info_selection
                    WHERE  project_id = #{projectId}
                    AND
                    customer_id IN (SELECT customer_id FROM customer_info u WHERE project_id = #{projectId}
                    <if test="customerSqlSegment != null and customerSqlSegment != ''">
                        and ${customerSqlSegment}
                    </if> )
                    AND  field_id IN
                    <foreach item="fieldId" collection="selectionFieldIds" open="(" separator="," close=")">
                        #{fieldId}
                    </foreach>
                    GROUP BY project_id, customer_id, field_id
                    ),
                    aggregated_selection AS (
                    SELECT
                    project_id,
                    customer_id,
                    CONCAT('{', GROUP_CONCAT(CONCAT('"', field_id, '":', choices) SEPARATOR ','), '}') AS selection_data
                    FROM
                    selection_fields
                    GROUP BY
                    project_id, customer_id
                    ),
                    filtered_fs AS (
                    SELECT
                    s.project_id,
                    s.customer_id,
                    MIN(fs.choice_name) AS choice_name -- 确保每个 user 唯一
                    FROM
                    customer_info u
                    LEFT JOIN
                    selection_fields s
                    ON
                    u.project_id = s.project_id AND
                    u.customer_id = s.customer_id
                    LEFT JOIN
                    project_field_selection fs
                    ON
                    s.project_id = fs.project_id AND s.field_id = fs.field_id AND s.min_choice_id = fs.choice_id
                    WHERE
                    s.project_id = #{projectId}
                    <if test="customerSqlSegment != null and customerSqlSegment != ''">
                        and ${customerSqlSegment}
                    </if>
                    GROUP BY
                    s.project_id,  s.customer_id
                    )
                </if>
            </trim>
        </if>
        select DISTINCT u.key_id, u.customer_id, u.customer_name, u.customer_status, u.created_time, u.modified_time,u.customer_type,u.customer_industry,u.customer_level,u.customer_address
        <if test="textFieldIds != null and textFieldIds.size() > 0">
            , COALESCE(t.text_values, '{}') AS text_fields, min_text
        </if>
        <if test="dateTimeFieldIds != null and dateTimeFieldIds.size() > 0">
            ,COALESCE(d.datetime_values, '{}') AS datetime_fields, min_datetime
        </if>
        <if test="selectionFieldIds != null and selectionFieldIds.size() > 0">
            ,(
            SELECT CONCAT('{', GROUP_CONCAT(
            CONCAT('"', field_id, '":', choices)
            SEPARATOR ','
            ), '}')
            FROM selection_fields s
            WHERE s.project_id = #{projectId} AND s.customer_id = u.customer_id
            ) AS selection_fields,
            fs.choice_name -- 添加到 SELECT 列表中
        </if>
        from customer_info u
        <if test="textFieldIds != null and textFieldIds.size() > 0">
            LEFT JOIN text_fields t ON t.project_id = #{projectId} AND u.customer_id = t.customer_id
        </if>
        <if test="dateTimeFieldIds != null and dateTimeFieldIds.size() > 0">
            LEFT JOIN datetime_fields d ON d.project_id = #{projectId} AND u.customer_id = d.customer_id
        </if>
        <if test="selectionFieldIds != null and selectionFieldIds.size() > 0">
            LEFT JOIN aggregated_selection s  ON  s.project_id = #{projectId} AND u.customer_id = s.customer_id
            LEFT JOIN filtered_fs fs ON fs.project_id = #{projectId} AND u.customer_id = fs.customer_id
        </if>
        ${ew.getCustomSqlSegment}
    </select>
    <sql id="selectCustomerInfoVo">
        select key_id, project_id, customer_id, customer_name, customer_status,
               customer_notes, created_time, modified_time
        from customer_info
    </sql>

    <select id="selectByProjectAndCustomer" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        where project_id = #{projectId} and customer_id = #{customerId}
    </select>
    <select id="getCustomerInfoDetail" resultMap="CustomerInfoResult">
        WITH
        base_customer_info AS (
        select u.key_id, u.project_id, u.customer_id, u.customer_name, u.customer_status,
               u.customer_notes, u.created_time, u.modified_time,u.customer_type,u.customer_industry,u.customer_level,u.customer_address
        FROM customer_info u
        WHERE u.customer_id= #{customerId} and u.project_id = #{projectId}
        ),
        datetime_fields AS (
        SELECT
        project_id,
        customer_id,
        GROUP_CONCAT(
        CONCAT('"', field_id, '":"', datetime, '"')
        SEPARATOR ','
        ) as datetime_values
        FROM customer_info_datetime
        WHERE project_id = #{projectId} AND customer_id = #{customerId}
        GROUP BY project_id, customer_id
        ),
        text_fields AS (
        SELECT
        project_id,
        customer_id,
        GROUP_CONCAT(
        CONCAT('"', field_id, '":"', REPLACE(text, '"', '\\"'), '"')
        SEPARATOR ','
        ) as text_values
        FROM customer_info_text
        WHERE project_id = #{projectId} AND customer_id = #{customerId}
        GROUP BY project_id, customer_id
        ),
        selection_fields AS (
        SELECT
        project_id,
        customer_id,
        field_id,
        CONCAT('[', GROUP_CONCAT(choice_id ORDER BY choice_id), ']') as choices
        FROM customer_info_selection
        WHERE project_id = #{projectId} AND customer_id = #{customerId}
        GROUP BY project_id, customer_id,field_id
        )
        SELECT
        u.*,
        CONCAT('{', COALESCE(d.datetime_values, ''), '}') as datetime_fields,
        CONCAT('{', COALESCE(t.text_values, ''), '}') as text_fields,
        (
        SELECT CONCAT('{',
        GROUP_CONCAT(
        CONCAT('"', field_id, '":', choices)
        SEPARATOR ','
        ),
        '}')
        FROM selection_fields s2
        WHERE s2.customer_id = u.customer_id AND s2.project_id = u.project_id
        ) as selection_fields
        FROM base_customer_info u
        LEFT JOIN datetime_fields d ON  u.project_id = d.project_id AND u.customer_id = d.customer_id
        LEFT JOIN text_fields t ON u.project_id = t.project_id AND u.customer_id = t.customer_id
    </select>
    <select id="selectByProjectId" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        where project_id = #{projectId}
        order by customer_id
    </select>

    <select id="selectByStatus" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        where project_id = #{projectId} and customer_status = #{customerStatus}
        order by customer_id
    </select>

    <select id="selectByNameLike" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        where project_id = #{projectId}
        and customer_name like concat('%', #{customerName}, '%')
        order by customer_id
    </select>

</mapper>
