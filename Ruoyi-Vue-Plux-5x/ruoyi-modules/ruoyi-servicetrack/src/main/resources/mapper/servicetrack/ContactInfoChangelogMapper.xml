<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.ContactInfoChangelogMapper">

    <resultMap type="org.dromara.servicetrack.domain.vo.ContactInfoChangelogVo" id="ContactInfoChangelogResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="changelogId" column="changelog_id"/>
        <result property="contactId" column="contact_id"/>
        <result property="logTime" column="log_time"/>
        <result property="changedById" column="changedby_id"/>
        <result property="contactName" column="contact_name"/>
        <result property="ChangedByUserName" column="changed_by_user_name"/>
        <result property="ChangedByUserAvatarUrl" column="changed_by_user_avatar_url"/>
    </resultMap>

    <sql id="selectContactInfoChangelogVo">
        select cic.key_id, cic.project_id, cic.changelog_id, cic.contact_id, 
               cic.log_time, cic.changedby_id,
               ci.contact_name,
               u.nick_name as changed_by_user_name,
               u.avatar as changed_by_user_avatar_url
        from contact_info_changelog cic
        left join contact_info ci on ci.project_id = cic.project_id and ci.contact_id = cic.contact_id
        left join sys_user u on u.external_user_id = cic.changedby_id
    </sql>

    <select id="selectByProjectAndContact" resultMap="ContactInfoChangelogResult">
        <include refid="selectContactInfoChangelogVo"/>
        where cic.project_id = #{projectId} and cic.contact_id = #{contactId}
        order by cic.changelog_id desc
    </select>

    <select id="selectByProjectId" resultMap="ContactInfoChangelogResult">
        <include refid="selectContactInfoChangelogVo"/>
        where cic.project_id = #{projectId}
        order by cic.log_time desc, cic.changelog_id desc
    </select>

    <select id="selectByChangelogId" resultMap="ContactInfoChangelogResult">
        <include refid="selectContactInfoChangelogVo"/>
        where cic.project_id = #{projectId} 
        and cic.changelog_id = #{changelogId} 
        and cic.contact_id = #{contactId}
    </select>

</mapper>
