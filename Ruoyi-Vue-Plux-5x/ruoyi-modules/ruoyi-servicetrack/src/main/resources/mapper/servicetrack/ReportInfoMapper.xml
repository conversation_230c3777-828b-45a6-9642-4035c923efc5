<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.ReportInfoMapper">

    <resultMap type="org.dromara.servicetrack.domain.vo.ReportInfoVo" id="ReportInfoResult">
    </resultMap>

    <sql id="selectReportInfoVo">
        select key_id, project_id, report_id, report_type, target_project_id, folder_id,
               report_name, report_description, created_date, created_by, modified_date,
               modified_by, report_setting
        from report_info
    </sql>

    <!-- 根据项目ID和文件夹ID查询报告列表 -->
    <select id="getReportListByProjectIdAndFolderId" resultMap="ReportInfoResult">
        <include refid="selectReportInfoVo"/>
        <where>
            project_id = #{projectId}
            <choose>
                <when test="folderId != null">
                    and folder_id = #{folderId}
                </when>
                <otherwise>
                    and folder_id is null
                </otherwise>
            </choose>
        </where>
        order by report_name asc
    </select>

    <select id="getReportInfo" resultMap="ReportInfoResult">
        <include refid="selectReportInfoVo"/>
        where project_id = #{projectId} and report_id = #{reportId}
    </select>

</mapper>
