<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.UserSettingMapper">
    <resultMap id="UserSettingResult" type="org.dromara.servicetrack.domain.vo.UserSettingVo">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="userId" column="user_id"/>
        <result property="settingId" column="setting_id"/>
        <result property="settingName" column="setting_name"/>
        <result property="settingOption" column="setting_option"/>
        <result property="settingContent" column="setting_content"/>
    </resultMap>

</mapper>
