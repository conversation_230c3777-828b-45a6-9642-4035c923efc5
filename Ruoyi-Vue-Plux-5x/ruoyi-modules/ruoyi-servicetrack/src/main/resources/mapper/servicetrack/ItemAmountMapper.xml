<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.ItemAmountMapper">
    <!-- 查询所有 -->
    <select id="selectAll" resultType="org.dromara.servicetrack.domain.vo.ItemAmountVo">
        SELECT * FROM item_amount
    </select>

    <!-- 根据主键查询 -->
    <select id="selectByPk" parameterType="map" resultType="org.dromara.servicetrack.domain.vo.ItemAmountVo">
        SELECT * FROM item_amount WHERE project_id = #{projectId} AND item_id = #{itemId} AND field_id = #{fieldId}
    </select>

</mapper>
