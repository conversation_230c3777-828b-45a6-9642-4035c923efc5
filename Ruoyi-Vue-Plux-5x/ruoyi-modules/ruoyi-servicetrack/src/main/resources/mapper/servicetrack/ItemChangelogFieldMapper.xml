<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.ItemChangelogFieldMapper">
    <resultMap type="org.dromara.servicetrack.domain.vo.ItemChangelogFieldVo" id="ItemChangelogFieldResult">
        <result column="logTime" property="log_time"/>
        <result column="changedById" property="changedby_id"/>
    </resultMap>
    <select id="selectItemChangelogField" resultMap="ItemChangelogFieldResult">
        select cf.changelog_id, cf.field_id,cf.change_from, cf.change_to,cf.description,cl.log_time,cl.changedby_id from item_changelog_field cf
        left join item_changelog cl on cf.project_id = cl.project_id and cf.item_id = cl.item_id and cl.changelog_id = cf.changelog_id
        where  cf.project_id = #{projectId} and cf.item_id = #{itemId}
        order by cl.changelog_id desc
    </select>
</mapper>
