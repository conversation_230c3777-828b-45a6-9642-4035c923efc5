<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.ItemHistoryMapper">
    <resultMap type="org.dromara.servicetrack.domain.vo.ItemHistoryVo" id="ItemHistoryResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="seqNo" column="seq_id"/>
        <result property="itemId" column="item_id"/>
        <result property="dateTime" column="datetime"/>
        <result property="userId" column="user_id"/>
        <result property="stateFrom" column="state_from"/>
        <result property="stateTo" column="state_to"/>
        <result property="ownerFrom" column="owner_from"/>
        <result property="ownerTo" column="owner_to"/>
    </resultMap>
</mapper>
