<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.ContactInfoDateTimeMapper">

    <resultMap type="org.dromara.servicetrack.domain.vo.ContactInfoDateTimeVo" id="ContactInfoDateTimeResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="contactId" column="contact_id"/>
        <result property="fieldId" column="field_id"/>
        <result property="dateTime" column="datetime"/>
        <result property="contactName" column="contact_name"/>
        <result property="fieldName" column="field_name"/>
        <result property="fieldType" column="field_type"/>
    </resultMap>

    <sql id="selectContactInfoDateTimeVo">
        select cidt.key_id, cidt.project_id, cidt.contact_id, cidt.field_id, cidt.datetime,
               ci.contact_name,
               pf.field_name, pf.field_type
        from contact_info_datetime cidt
        left join contact_info ci on ci.project_id = cidt.project_id and ci.contact_id = cidt.contact_id
        left join project_field pf on pf.project_id = cidt.project_id and pf.field_id = cidt.field_id
    </sql>

    <select id="selectByProjectAndContact" resultMap="ContactInfoDateTimeResult">
        <include refid="selectContactInfoDateTimeVo"/>
        where cidt.project_id = #{projectId} and cidt.contact_id = #{contactId}
        order by cidt.field_id, cidt.datetime desc
    </select>

    <select id="selectByProjectId" resultMap="ContactInfoDateTimeResult">
        <include refid="selectContactInfoDateTimeVo"/>
        where cidt.project_id = #{projectId}
        order by cidt.contact_id, cidt.field_id, cidt.datetime desc
    </select>

    <select id="selectByFieldId" resultMap="ContactInfoDateTimeResult">
        <include refid="selectContactInfoDateTimeVo"/>
        where cidt.project_id = #{projectId} 
        and cidt.contact_id = #{contactId} 
        and cidt.field_id = #{fieldId}
        order by cidt.datetime desc
    </select>

    <select id="selectByDateRange" resultMap="ContactInfoDateTimeResult">
        <include refid="selectContactInfoDateTimeVo"/>
        where cidt.project_id = #{projectId} 
        and cidt.contact_id = #{contactId}
        <if test="startDate != null">
            and cidt.datetime >= #{startDate}
        </if>
        <if test="endDate != null">
            and cidt.datetime &lt;= #{endDate}
        </if>
        order by cidt.datetime desc, cidt.field_id
    </select>

</mapper>
