<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.ItemTempInfoMapper">
    <resultMap type="org.dromara.servicetrack.domain.vo.ItemTempInfoVo" id="ItemTempInfoResult">
    </resultMap>
    <resultMap type="org.dromara.servicetrack.domain.vo.ItemTempInfoListVo" id="ItemTempInfoListResult">
        <id column="template_id" property="templateId"/>
        <id column="key_id" property="id"/>
        <result column="template_name" property="templateName"/>
        <result column="group_id" property="groupId"/>
        <result column="display_order" property="displayOrder"/>
    </resultMap>
    <select id="getItemTempInfoList" parameterType="map" resultMap="ItemTempInfoListResult">
        select t.key_id, t.template_id, t.template_name,t.template_type, tg.group_id, tg.display_order
        from item_temp_info t
        left join item_temp_groupsetting tg
        on t.work_project_id = tg.project_id
        and t.template_id = tg.template_id
        where t.project_id = #{projectId}
        and t.work_project_id = #{workProjectId}
        <if test="groupId != null and groupId > 0">
            and tg.group_id = #{groupId}
        </if>
        <if test="templateType !=null and templateType > 0">
            and t.template_type = #{templateType}
        </if>
        order by tg.group_id, tg.display_order
    </select>
</mapper>
