<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.SystemSettingMapper">

    <resultMap type="org.dromara.servicetrack.domain.vo.SystemSettingVo" id="SystemSettingResult">
        <id column="key_id" property="id" />
        <result column="setting_id" property="settingId" />
        <result column="setting_name" property="settingName" />
        <result column="setting_option" property="settingOption" />
        <result column="setting_content" property="settingContent" />
    </resultMap>

    <sql id="selectSystemSettingVo">
        select key_id, setting_id, setting_name, setting_option, setting_content from system_setting
    </sql>

    <select id="selectSystemSettingList" resultMap="SystemSettingResult">
        <include refid="selectSystemSettingVo"/>
        order by setting_id
    </select>

    <select id="selectBySettingId" parameterType="Integer" resultMap="SystemSettingResult">
        <include refid="selectSystemSettingVo"/>
        where setting_id = #{settingId}
    </select>

    <select id="selectBySettingIds"  resultMap="SystemSettingResult">
        <include refid="selectSystemSettingVo"/>
        where setting_id in
        <foreach collection="settingIds" item="settingId" open="(" separator="," close=")">
            #{settingId}
        </foreach>
    </select>

    <select id="selectBySettingName" parameterType="String" resultMap="SystemSettingResult">
        <include refid="selectSystemSettingVo"/>
        where setting_name like concat('%', #{settingName}, '%')
        order by setting_id
    </select>

    <select id="selectBySettingOption" parameterType="Integer" resultMap="SystemSettingResult">
        <include refid="selectSystemSettingVo"/>
        where setting_option = #{settingOption}
        order by setting_id
    </select>
</mapper>
