<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.servicetrack.mapper.ContactInfoSelectionMapper">

    <resultMap type="org.dromara.servicetrack.domain.vo.ContactInfoSelectionVo" id="ContactInfoSelectionResult">
        <id property="id" column="key_id"/>
        <result property="projectId" column="project_id"/>
        <result property="contactId" column="contact_id"/>
        <result property="fieldId" column="field_id"/>
        <result property="choiceId" column="choice_id"/>
        <result property="contactName" column="contact_name"/>
        <result property="choiceName" column="choice_name"/>
        <result property="fieldName" column="field_name"/>
        <result property="fieldType" column="field_type"/>
    </resultMap>

    <sql id="selectContactInfoSelectionVo">
        select cis.key_id, cis.project_id, cis.contact_id, cis.field_id, cis.choice_id,
               ci.contact_name,
               pfs.choice_name,
               pf.field_name, pf.field_type
        from contact_info_selection cis
        left join contact_info ci on ci.project_id = cis.project_id and ci.contact_id = cis.contact_id
        left join project_field_selection pfs on pfs.project_id = cis.project_id 
                                               and pfs.field_id = cis.field_id 
                                               and pfs.choice_id = cis.choice_id
        left join project_field pf on pf.project_id = cis.project_id and pf.field_id = cis.field_id
    </sql>

    <select id="selectByProjectAndContact" resultMap="ContactInfoSelectionResult">
        <include refid="selectContactInfoSelectionVo"/>
        where cis.project_id = #{projectId} and cis.contact_id = #{contactId}
        order by cis.field_id, cis.choice_id
    </select>

    <select id="selectByProjectId" resultMap="ContactInfoSelectionResult">
        <include refid="selectContactInfoSelectionVo"/>
        where cis.project_id = #{projectId}
        order by cis.contact_id, cis.field_id, cis.choice_id
    </select>

    <select id="selectByFieldId" resultMap="ContactInfoSelectionResult">
        <include refid="selectContactInfoSelectionVo"/>
        where cis.project_id = #{projectId} 
        and cis.contact_id = #{contactId} 
        and cis.field_id = #{fieldId}
        order by cis.choice_id
    </select>

</mapper>
