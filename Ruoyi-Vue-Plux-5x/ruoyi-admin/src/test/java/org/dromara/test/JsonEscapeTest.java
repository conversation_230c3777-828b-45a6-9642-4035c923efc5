package org.dromara.test;

import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.fasterxml.jackson.core.type.TypeReference;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JSON转义测试案例
 *
 * <AUTHOR> Assistant
 */
@SpringBootTest
@DisplayName("JSON转义测试案例")
public class JsonEscapeTest {

    @DisplayName("测试 escapeJsonSpecialChars 方法处理反斜杠")
    @Test
    public void testEscapeJsonSpecialChars() {
        // 测试原始问题的JSON字符串
        String problematicJson = "{\"2\":\"<p>daddy555555</p><p>3333</p><p>33333</p>\",\"10007\":\"aaa\\nbbbb\",\"10010\":\"3333\"}";
        
        // 应该能够正确转义
        String escapedJson = StringUtils.escapeJsonSpecialChars(problematicJson);
        System.out.println("Original: " + problematicJson);
        System.out.println("Escaped:  " + escapedJson);
        
        // 验证转义后的JSON可以正确解析
        assertDoesNotThrow(() -> {
            Map<String, String> result = JsonUtils.parseObject(escapedJson, new TypeReference<Map<String, String>>() {});
            assertNotNull(result);
            assertEquals(3, result.size());
            assertEquals("<p>daddy555555</p><p>3333</p><p>33333</p>", result.get("2"));
            assertEquals("aaa\\nbbbb", result.get("10007"));
            assertEquals("3333", result.get("10010"));
        });
    }

    @DisplayName("测试各种特殊字符的转义")
    @Test
    public void testVariousSpecialChars() {
        // 测试各种特殊字符
        String testJson = "{\"field1\":\"line1\\nline2\",\"field2\":\"tab\\there\",\"field3\":\"quote\\\"here\"}";
        
        String escapedJson = StringUtils.escapeJsonSpecialChars(testJson);
        System.out.println("Test JSON: " + testJson);
        System.out.println("Escaped:   " + escapedJson);
        
        // 验证转义后的JSON可以正确解析
        assertDoesNotThrow(() -> {
            Map<String, String> result = JsonUtils.parseObject(escapedJson, new TypeReference<Map<String, String>>() {});
            assertNotNull(result);
            assertEquals(3, result.size());
        });
    }

    @DisplayName("测试空字符串和null值")
    @Test
    public void testNullAndEmpty() {
        assertNull(StringUtils.escapeJsonSpecialChars(null));
        assertEquals("", StringUtils.escapeJsonSpecialChars(""));
        assertEquals("   ", StringUtils.escapeJsonSpecialChars("   "));
    }

    @DisplayName("测试正常JSON不被破坏")
    @Test
    public void testValidJsonNotBroken() {
        String validJson = "{\"name\":\"John\",\"age\":30,\"city\":\"New York\"}";
        String escapedJson = StringUtils.escapeJsonSpecialChars(validJson);
        
        // 正常的JSON应该不被改变（或者改变后仍然有效）
        assertDoesNotThrow(() -> {
            Map<String, Object> result = JsonUtils.parseObject(escapedJson, new TypeReference<Map<String, Object>>() {});
            assertNotNull(result);
            assertEquals(3, result.size());
        });
    }
}
