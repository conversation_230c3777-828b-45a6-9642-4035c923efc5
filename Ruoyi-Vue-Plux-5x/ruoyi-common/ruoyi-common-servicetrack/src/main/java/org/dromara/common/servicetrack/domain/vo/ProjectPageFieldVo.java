package org.dromara.common.servicetrack.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.servicetrack.domain.ProjectPageField;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 页面字段对象VO
 *
 * <AUTHOR> fei
 */
@Data
@NoArgsConstructor
@AutoMapper(target = ProjectPageField.class)
public class ProjectPageFieldVo extends ProjectPageFieldMVo {

    /**
     * page row
     */
    private Integer pageRow;

    /**
     * page column
     */
    private Integer pageColumn;

}
