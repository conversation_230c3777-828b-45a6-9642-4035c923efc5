package org.dromara.common.servicetrack.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.servicetrack.domain.ProjectPageFieldM;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 项目页面字段映射视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AutoMapper(target = ProjectPageFieldM.class)
public class ProjectPageFieldMVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    @JsonIgnore
    private Integer projectId;

    /**
     * 页面ID
     */
    private Integer pageId;

    /**
     * 页面名称
     */
    private String pageName;

    /**
     * 字段ID
     */
    private Integer fieldId;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段类型
     */
    private Integer fieldType;

    /**
     * 字段子类型
     */
    private Integer fieldSubtype;

    /**
     * 显示顺序
     */
    private Integer displayOrder;

    /**
     * 模块ID
     */
    private Integer moduleId;

    /*
     *  formula
     */
    private String formula;

    /*
     *  parent field id
     */
    private Integer parentFieldId;
    /*
     *  child field ids
     */
    private List<Integer> childFieldIds;

    public ProjectPageFieldVo toPageFieldVo() {
        ProjectPageFieldVo vo = new ProjectPageFieldVo();
        vo.setId(this.getId());
        vo.setProjectId(this.getProjectId());
        vo.setPageId(this.getPageId());
        vo.setFieldId(this.getFieldId());
        vo.setFieldName(this.getFieldName());
        vo.setFieldType(this.getFieldType());
        vo.setFieldSubtype(this.getFieldSubtype());
        vo.setDisplayOrder(this.getDisplayOrder());
        vo.setModuleId(this.getModuleId());
        vo.setFormula(this.getFormula());
        vo.setParentFieldId(this.getParentFieldId());
        vo.setChildFieldIds(this.getChildFieldIds());
        return vo;
    }
}
