package org.dromara.common.servicetrack.service;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.servicetrack.domain.bo.ProjectFieldSelectionBo;
import org.dromara.common.servicetrack.domain.bo.ProjectPageFieldBo;
import org.dromara.common.servicetrack.domain.bo.WorkflowStateBo;
import org.dromara.common.servicetrack.domain.bo.WorkflowTransitionBo;
import org.dromara.common.servicetrack.domain.vo.*;

import java.util.List;
/**
 * 项目数据 服务层
 * */
public interface IProjectDataService {

    /**
     * 根据项目id查询项目信息
     */
    ProjectInfoVo selectProjectInfoById(Integer projectId);

    /**
     * 根据项目id查询项目ProjectId
     */
    List<Integer> getWorkProjectIds(Integer baseProjectId);
    /**
     * 查询页面字段列表
     */
    List<ProjectPageFieldVo> selectPageFieldList(ProjectPageFieldBo bo);

    /**
     * 查询mobile页面字段列表
     */
    List<ProjectPageFieldMVo> selectPageFieldMList(ProjectPageFieldBo bo);
    /**
     * 查询字段选项列表
     */
    List<ProjectFieldSelectionVo> selectFieldSelctionList(ProjectFieldSelectionBo bo);

    /**
     * 查询工作流状态列表
     */
    List<WorkflowStateVo> selectStateList(WorkflowStateBo bo);

    /**
     * 查询工作流流转列表
     */
    List<WorkflowTransitionVo> selectTransitionList(WorkflowTransitionBo bo);

    /**
     * 获取工作流字段信息
     */
    List<WorkflowStateFieldVo> getWorkflowStateFieldList(Integer projectId);
    /**
     * 获取工作流状态负责人信息
     */
    List<WorkflowStateOwnersVo> getWorkflowStateOwnersList(Integer projectId);
    /**
     * 获取工作流流转字段信息
     */
    List<WorkflowTransitionFieldVo> getWorkflowTransitionFieldList(Integer projectId);
    /**
     * 获取工作流流转权限信息
     */
    List<WorkflowTransitionPermissionVo> getWorkflowTransitionPermissionList(Integer projectId);
    /**
     * 查询项目成员列表
     */
    List<ProjectMemberVo> selectMemberList(Integer projectId);

    /**
     * 查询系统用户列表
     */
    List<SysUserSimpleInfoVo> selectSysUserList();

    /**
     * 根据部门ID查询部门名称
     */
    String getDeptNameById(Long deptId);

    /**
     * 根据项目ID查询项目类型
     */
    List<ProjectItemTypeVo> selectItemTypeList(Integer projectId);

    List<NotificationRuleListItemVo> selectNotificationRuleList(Integer projectId);

    List<CustomerSimpleInfoVo> selectCustomerSimpleInfoList(Integer projectId);

    /**
     * 根据项目ID查询账户权限
     */
    List<ProjectAccountPermissionVo> selectAccountPermissionList(Integer projectId);

    /**
     * 查询项目字段父子关系列表
     *
     * @param projectId 项目ID
     * @return 项目字段父子关系列表
     */
    List<ProjectFieldPcVo> selectFieldPcList(Integer projectId);
    /**
     * 查询项目字段父子选择项关系列表
     *
     * @param projectId 项目ID
     * @return 项目字段父子选择项关系列表
     */
    List<ProjectFieldPcSelectionVo> selectFieldPcSelectionList(Integer projectId);

    /**
     * 查询项目设置列表
     *
     * @param projectId 项目ID
     * @return 项目设置列表
     */
    List<ProjectSettingInCacheVo> getProjectSettingList(Integer projectId);

    /**
     * 查询项目分组列表
     *
     * @param projectId 项目ID
     * @return 项目分组列表
     */
    List<ProjectGroupVo> selectGroupList(Integer projectId);
    /**
     * 查询项目分组用户
     */
    List<ProjectGroupUserVo> selectGroupUserList(Integer projectId);
}
