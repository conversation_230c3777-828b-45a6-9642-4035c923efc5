package org.dromara.common.servicetrack.service;

import org.dromara.common.servicetrack.domain.bo.ProjectPageFieldBinderBo;
import org.dromara.common.servicetrack.domain.bo.ProjectPageFieldBo;
import org.dromara.common.servicetrack.domain.bo.ProjectPageFieldMBinderBo;
import org.dromara.common.servicetrack.domain.bo.ProjectPageFieldMoveRowBo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageFieldMVo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageFieldVo;

import java.util.Collection;
import java.util.List;

/**
 * 页面字段管理 服务层
 *
 * <AUTHOR> fei
 * */
public interface IProjectPageFieldServiceEx {
    /**
     * 根据条件查询页面字段列表
     *
     * @param field 页面字段信息
     * @return 页面字段集合信息
     */
    List<ProjectPageFieldVo> selectPageFieldList(ProjectPageFieldBo field,boolean fromCache);
    /**
     * 根据条件查询页面字段列表
     *
     * @param field 页面字段信息
     * @return 页面字段集合信息
     */
    List<ProjectPageFieldMVo> selectPageFieldMList(ProjectPageFieldBo field, boolean fromCache);
    /**
     * 根据条件查询页面字段列表
     *
     * @param projectId 项目ID
     * @param pageId 页面ID
     * @return 页面字段集合信息
     */
    List<ProjectPageFieldVo> selectPageFieldList(Integer projectId,Integer pageId);

    /**
     * 根据条件查询Mobile页面字段列表
     *
     * @param projectId 项目ID
     * @param pageId 页面ID
     * @return 页面字段集合信息
     */
    List<ProjectPageFieldMVo> selectPageFieldMList(Integer projectId,Integer pageId);
    /**
     * 新增项目页面字段
     *
     */
    Boolean insertByBo(ProjectPageFieldBo bo);

    /**
     * 修改项目页面字段
     *
     */
    Boolean updateByBo(ProjectPageFieldBinderBo bo);

    /**
     * 修改项目页面字段
     *
     */
    Boolean updateMobileFieldByBo(ProjectPageFieldMBinderBo bo);
    /**
     * 修改项目页面行字段
     *
     */
    Boolean moveRowByBo(ProjectPageFieldMoveRowBo bo);
    /**
     * 修改项目页面列字段
     *
     */
    Boolean moveColumnByBo(ProjectPageFieldBinderBo bo);
    /**
     * 删除项目页面字段
     *
     * @param keyIds 主键串
     */
    Boolean deleteWithValidByIds(Integer projectId,Collection<Long> keyIds, Boolean isValid);
}
