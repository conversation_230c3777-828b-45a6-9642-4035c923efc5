package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectPageFieldM;
import org.dromara.common.servicetrack.domain.bo.ProjectPageFieldMBo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageFieldMVo;

import java.util.List;

/**
 * 项目页面字段映射 数据层
 *
 * <AUTHOR>
 */
public interface ProjectPageFieldMMapper extends BaseMapperPlus<ProjectPageFieldM, ProjectPageFieldMVo> {

    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ProjectPageFieldM> buildWrapper(ProjectPageFieldMBo bo) {
        LambdaQueryWrapper<ProjectPageFieldM> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, ProjectPageFieldM::getProjectId, bo.getProjectId());
        lqw.eq(bo.getPageId() != null, ProjectPageFieldM::getPageId, bo.getPageId());
        lqw.eq(bo.getFieldId() != null, ProjectPageFieldM::getFieldId, bo.getFieldId());
        lqw.eq(bo.getDisplayOrder() != null, ProjectPageFieldM::getDisplayOrder, bo.getDisplayOrder());
        lqw.orderByAsc(ProjectPageFieldM::getDisplayOrder);
        return lqw;
    }

    /**
     * 查询项目页面字段映射列表
     *
     * @param queryWrapper 查询条件
     * @return 项目页面字段映射列表
     */
    @DataPermission({
        @DataColumn(key = "pageName", value = "f.page_id"),
        @DataColumn(key = "fieldName", value = "f.field_id")
    })
    List<ProjectPageFieldMVo> selectPageFieldMList(@Param(Constants.WRAPPER) Wrapper<ProjectPageFieldM> queryWrapper);

    /**
     * 根据项目ID和页面ID查询字段映射列表
     *
     * @param projectId 项目ID
     * @param pageId 页面ID
     * @return 字段映射列表
     */
    List<ProjectPageFieldMVo> selectByProjectAndPage(@Param("projectId") Integer projectId, 
                                                    @Param("pageId") Integer pageId);

    /**
     * 根据项目ID查询字段映射列表
     *
     * @param projectId 项目ID
     * @return 字段映射列表
     */
    List<ProjectPageFieldMVo> selectByProject(@Param("projectId") Integer projectId);

    /**
     * 批量删除项目页面字段映射
     *
     * @param projectId 项目ID
     * @param pageIds 页面ID列表
     */
    void deleteByPageIds(@Param("projectId") Integer projectId, 
                        @Param("pageIds") List<Integer> pageIds);

    /**
     * 批量删除项目页面字段映射（根据字段ID）
     *
     * @param projectId 项目ID
     * @param fieldIds 字段ID列表
     */
    void deleteByFieldIds(@Param("projectId") Integer projectId, 
                         @Param("fieldIds") List<Integer> fieldIds);

    /**
     * 更新显示顺序
     *
     * @param projectId 项目ID
     * @param pageId 页面ID
     * @param fieldId 字段ID
     * @param displayOrder 新的显示顺序
     */
    void updateDisplayOrder(@Param("projectId") Integer projectId,
                           @Param("pageId") Integer pageId,
                           @Param("fieldId") Integer fieldId,
                           @Param("displayOrder") Integer displayOrder);
}
