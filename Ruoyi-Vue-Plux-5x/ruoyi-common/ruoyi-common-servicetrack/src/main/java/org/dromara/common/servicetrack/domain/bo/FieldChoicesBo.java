package org.dromara.common.servicetrack.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 字段选项管理业务对象 field_choices
 * <AUTHOR>
 */
@Data
public class FieldChoicesBo{
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 字段IDs
     */
    @NotNull(message = "字段IDs不能为空")
    private List<Integer> fieldIds;

    /**
     * 是否允许为空
     */
    private Boolean allowEmpty;
}
