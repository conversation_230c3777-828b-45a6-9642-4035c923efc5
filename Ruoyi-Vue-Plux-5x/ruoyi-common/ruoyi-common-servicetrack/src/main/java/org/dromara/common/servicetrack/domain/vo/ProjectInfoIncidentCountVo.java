package org.dromara.common.servicetrack.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class ProjectInfoIncidentCountVo implements Serializable {
    /*
     * 1: open and current owner by current user, 2: open and submitted by current user,
     *  3: closed and submitted by current user
     */
    private Integer selectOption;
    private Integer count;
}
