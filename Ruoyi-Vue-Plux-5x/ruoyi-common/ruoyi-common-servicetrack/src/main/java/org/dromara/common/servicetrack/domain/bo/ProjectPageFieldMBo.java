package org.dromara.common.servicetrack.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.servicetrack.domain.ProjectPageFieldM;

/**
 * 项目页面字段映射业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProjectPageFieldM.class)
public class ProjectPageFieldMBo extends STBaseEntity {
    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    /**
     * 页面ID
     */
    @NotNull(message = "页面ID不能为空")
    private Integer pageId;

    /**
     * 字段ID
     */
    @NotNull(message = "字段ID不能为空")
    private Integer fieldId;

    /**
     * 显示顺序
     */
    @Min(value = 0, message = "显示顺序不能小于0")
    private Integer displayOrder;
}
