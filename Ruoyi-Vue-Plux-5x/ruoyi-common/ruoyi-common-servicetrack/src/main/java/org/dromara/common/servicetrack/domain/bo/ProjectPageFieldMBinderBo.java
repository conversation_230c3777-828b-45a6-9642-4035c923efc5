package org.dromara.common.servicetrack.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.List;

/**
 * 项目mobile页面字段映射绑定对象Bo
 *
 * <AUTHOR> fei
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectPageFieldMBinderBo extends STBaseEntity {
    @NotNull(message = "项目ID不能为空")
    private Integer projectId;

    @NotNull(message = "页面ID不能为空")
    private Integer pageId;

    List<ProjectPageFieldMInfoBo> pageFields;
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ProjectPageFieldMInfoBo extends STBaseEntity{

        private Long id;
        /**
         * 字段ID
         */
        @NotNull(message = "字段ID不能为空")
        private Integer fieldId;

        /**
         * 字段顺序
         */
        @NotNull(message = "字段顺序不能为空")
        private Integer fieldOrder;
    }
}
