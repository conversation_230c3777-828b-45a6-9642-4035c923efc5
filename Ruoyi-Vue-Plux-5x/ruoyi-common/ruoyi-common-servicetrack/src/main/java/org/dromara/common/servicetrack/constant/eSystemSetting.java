package org.dromara.common.servicetrack.constant;

public enum
eSystemSetting implements IValueEnum {
    System_Name(1),
    ;
    private final int value;
    /*
     *构造函数在定义常量时自动调用
     */
    eSystemSetting(int value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return value;
    }

    public static eSystemSetting from(Integer value) {
        return IValueEnum.valueOf(eSystemSetting.class, value);
    }
}
