package org.dromara.common.servicetrack.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jodd.introspector.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.common.servicetrack.domain.ProjectInfo;
import org.dromara.common.servicetrack.domain.bo.ProjectInfoBo;
import org.dromara.common.servicetrack.domain.vo.*;
import org.dromara.common.servicetrack.domain.ProjectAccountType;
import org.dromara.common.servicetrack.domain.bo.ProjectAccountTypeBo;

import java.util.List;

/**
 * 项目管理 数据层
 *
 * <AUTHOR> fei
 */
//@Mapper
public interface ProjectInfoMapper extends BaseMapperPlus<ProjectInfo, ProjectInfoVo> {

    /**
     * 获取查询包装器
     */
    default LambdaQueryWrapper<ProjectInfo> buildWrapper(ProjectInfoBo bo) {
        LambdaQueryWrapper<ProjectInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, ProjectInfo::getId, bo.getId());
        lqw.eq(bo.getProjectId() != null, ProjectInfo::getProjectId, bo.getProjectId());
        lqw.like(StringUtils.isNotBlank(bo.getProjectName()), ProjectInfo::getProjectName, bo.getProjectName());
        lqw.eq(StringUtils.isNotBlank(bo.getProjectKey()), ProjectInfo::getProjectKey, bo.getProjectKey());
        lqw.eq(bo.getProjectType() != null, ProjectInfo::getProjectType, bo.getProjectType());
        lqw.eq(bo.getBaseProjectId() != null, ProjectInfo::getBaseProjectId, bo.getBaseProjectId());
        lqw.eq(bo.getIsActive() != null, ProjectInfo::getIsActive, bo.getIsActive());
        return lqw;
    }

    /**
     * 获取项目账户类型查询包装器
     */
    default LambdaQueryWrapper<ProjectAccountType> buildProjectAccountTypeWrapper(ProjectAccountTypeBo bo) {
        LambdaQueryWrapper<ProjectAccountType> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProjectId() != null, ProjectAccountType::getProjectId, bo.getProjectId());
        lqw.eq(bo.getAccountTypeId() != null, ProjectAccountType::getAccountTypeId, bo.getAccountTypeId());
        lqw.eq(bo.getTypeId() != null, ProjectAccountType::getTypeId, bo.getTypeId());
        lqw.like(StringUtils.isNotBlank(bo.getAccountTypeName()), ProjectAccountType::getAccountTypeName, bo.getAccountTypeName());
        return lqw;
    }
    /**
     * 获取项目邮箱设置
     */
    String getEmailSetting(@Param("projectId") Integer projectId,@Param("settingId") Integer settingId);

    /*
     * 获取项目open and closed count
     */
    List<ProjectInfoOpenAndClosedCountVo> getOpenAndClosedCount(@Param("projectIds") List<Integer> projectIds);

    /*
     * 获取用户相关的工单数
     */
    List<ProjectInfoIncidentCountVo> getIncidentCountByUser(@Param("projectId") Integer projectId, @Param("userId") Integer userId,
                                                           @Param("openStateIds") List<Integer> openStateIds, @Param("closedStateIds") List<Integer> closedStateIds);

    /*
     * 获取项目设置列表
     */
    List<ProjectSettingInCacheVo> getProjectSettingList(@Param("projectId") Integer projectId,@Param("settingIds") List<Integer> settingIds);
}
