<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.ProjectPageFieldMMapper">

    <resultMap type="org.dromara.common.servicetrack.domain.vo.ProjectPageFieldMVo" id="ProjectPageFieldMResult">
        <id column="key_id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="page_id" property="pageId" />
        <result column="field_id" property="fieldId" />
        <result column="display_order" property="displayOrder" />
        <result column="page_name" property="pageName" />
        <result column="field_name" property="fieldName" />
        <result column="field_type" property="fieldType" />
        <result column="field_subtype" property="fieldSubtype" />
        <result column="module_id" property="moduleId" />
    </resultMap>

    <sql id="selectProjectPageFieldMVo">
        select f.key_id, f.project_id, f.page_id, f.field_id, f.display_order from project_page_field_m f
    </sql>

    <sql id="selectProjectPageFieldMWithDetailsVo">
        select f.key_id, f.project_id, f.page_id, f.field_id, f.display_order,
               p.page_name,
               COALESCE(fc.field_name, fs.field_name) as field_name,
               COALESCE(fc.field_type, fs.field_type) as field_type,
               COALESCE(fc.field_subtype, fs.field_subtype) as field_subtype,
               COALESCE(fc.module_id, fs.module_id) as module_id
        from project_page_field_m f
        left join project_page p on f.project_id = p.project_id and f.page_id = p.page_id
        left join project_field_custom fc on f.project_id = fc.project_id and f.field_id = fc.field_id
        left join project_field_system fs on f.project_id = fs.project_id and f.field_id = fs.field_id
    </sql>

    <select id="selectPageFieldMList" resultMap="ProjectPageFieldMResult">
        <include refid="selectProjectPageFieldMWithDetailsVo"/>
        ${ew.customSqlSegment}
    </select>

    <select id="selectByProjectAndPage" parameterType="Integer" resultMap="ProjectPageFieldMResult">
        <include refid="selectProjectPageFieldMWithDetailsVo"/>
        where f.project_id = #{projectId}
        <if test="pageId != null">
            and f.page_id = #{pageId}
        </if>
        order by f.display_order, f.field_id
    </select>

    <select id="selectByProject" parameterType="Integer" resultMap="ProjectPageFieldMResult">
        <include refid="selectProjectPageFieldMWithDetailsVo"/>
        where f.project_id = #{projectId}
        order by f.page_id, f.display_order, f.field_id
    </select>

    <delete id="deleteByPageIds">
        DELETE FROM project_page_field_m
        WHERE project_id = #{projectId}
        AND page_id IN
        <foreach collection="pageIds" item="pageId" open="(" separator="," close=")">
            #{pageId}
        </foreach>
    </delete>

    <delete id="deleteByFieldIds">
        DELETE FROM project_page_field_m
        WHERE project_id = #{projectId}
        AND field_id IN
        <foreach collection="fieldIds" item="fieldId" open="(" separator="," close=")">
            #{fieldId}
        </foreach>
    </delete>

    <update id="updateDisplayOrder">
        UPDATE project_page_field_m
        SET display_order = #{displayOrder}
        WHERE project_id = #{projectId}
        AND page_id = #{pageId}
        AND field_id = #{fieldId}
    </update>
</mapper>
