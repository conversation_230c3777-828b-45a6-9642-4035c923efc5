<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.common.servicetrack.mapper.NotificationRecipientEmailAddressMapper">
    <!-- 查询所有 -->
    <select id="selectAll" resultType="org.dromara.common.servicetrack.domain.vo.NotificationRecipientEmailAddressVo">
        SELECT * FROM notification_recipient_emailaddress
    </select>

    <!-- 根据主键查询 -->
    <select id="selectByPk" parameterType="map" resultType="org.dromara.common.servicetrack.domain.vo.NotificationRecipientEmailAddressVo">
        SELECT * FROM notification_recipient_emailaddress WHERE project_id = #{projectId} AND rule_id = #{ruleId}
    </select>

</mapper>
