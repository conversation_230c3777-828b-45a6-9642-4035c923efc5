<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi-vue-plus</artifactId>
        <groupId>org.dromara</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>ruoyi-common-bom</module>
        <module>ruoyi-common-social</module>
        <module>ruoyi-common-core</module>
        <module>ruoyi-common-doc</module>
        <module>ruoyi-common-excel</module>
        <module>ruoyi-common-idempotent</module>
        <module>ruoyi-common-job</module>
        <module>ruoyi-common-log</module>
        <module>ruoyi-common-mail</module>
        <module>ruoyi-common-mybatis</module>
        <module>ruoyi-common-oss</module>
        <module>ruoyi-common-ratelimiter</module>
        <module>ruoyi-common-redis</module>
        <module>ruoyi-common-satoken</module>
        <module>ruoyi-common-security</module>
        <module>ruoyi-common-sms</module>
        <module>ruoyi-common-web</module>
        <module>ruoyi-common-translation</module>
        <module>ruoyi-common-sensitive</module>
        <module>ruoyi-common-json</module>
        <module>ruoyi-common-encrypt</module>
        <module>ruoyi-common-tenant</module>
        <module>ruoyi-common-websocket</module>
        <module>ruoyi-common-sse</module>
        <module>ruoyi-common-servicetrack</module>
    </modules>

    <artifactId>ruoyi-common</artifactId>
    <packaging>pom</packaging>

    <description>
        common 通用模块
    </description>

</project>
