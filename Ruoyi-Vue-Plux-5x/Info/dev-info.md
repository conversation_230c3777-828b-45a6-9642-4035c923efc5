# Interface document
http://localhost:8080/swagger-ui/index.html
http://localhost:8080/v3/api-docs

# mvn 命令
mvn spring-boot:run
mvn clean install
mvn clean package
mvn clean compile
     java -jar target/yudao-ui-admin-vue3-1.0.0-snapshot.jar 

    java -jar ./ruoyi-admin/target/ruoyi-admin.jar

# dev-info
## 现在要将“Sys_user”表中的“external_user_id”字段加入到项目中，并将它在“SysUserController”中的“getInfo”方法中输出出去。注意这个字段是自增长的INT类型
	具体的做法：
		1.在SysUser实体类中添加了external_user_id字段的映射，使用@TableField注解指定了数据库字段名
		2.在SysUserVo中添加了对应的externalUserId字段
	由于SysUserVo使用了@AutoMapper注解，字段会自动从SysUser映射到SysUserVo
	SysUserController的getInfo方法返回的UserInfoVo中包含了SysUserVo，所以externalUserId字段会自动在接口响应中输出

## 如果需要增加一个已经存在db中的新表到项目中，并用一个controller的一个api导出它。这个表"st_incident"
CREATE TABLE st_incident (
    id                BIGINT       NOT NULL COMMENT 'id',
    incident_id       INT          NOT NULL COMMENT 'incidentid',
    tenant_id         VARCHAR(20)  NOT NULL COMMENT '租户编号',
    contact_user_name VARCHAR(20)               COMMENT '联系人',
    contact_phone     VARCHAR(20)               COMMENT '联系电话',
    company_name      VARCHAR(50)               COMMENT '企业名称',
    license_number    VARCHAR(30)               COMMENT '统一社会信用代码',
    address           VARCHAR(200)              COMMENT '地址',
    intro             VARCHAR(200)              COMMENT '企业简介',
    domain            VARCHAR(200)              COMMENT '域名',
    remark            VARCHAR(200)              COMMENT '备注',
    package_id        BIGINT                    COMMENT '租户套餐编号',
    expire_time       DATETIME                  COMMENT '过期时间',
    account_count     INT           DEFAULT -1  COMMENT '用户数量（-1不限制）',
    status            CHAR(1)       DEFAULT '0' COMMENT '租户状态（0正常 1停用）',
    del_flag          CHAR(1)       DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    create_dept       BIGINT                    COMMENT '创建部门',
    create_by         BIGINT                    COMMENT '创建者',
    create_time       DATETIME                  COMMENT '创建时间',
    update_by         BIGINT                    COMMENT '更新者',
    update_time       DATETIME                  COMMENT '更新时间',
    PRIMARY KEY (incident_id, id)
) ENGINE=InnoDB COMMENT='事件表';

已完成事件表(st_incident)的后端接口实现，提供以下API接口：

查询事件列表
接口：GET /system/incident/list
权限：system:incident:list
支持分页查询和条件过滤
支持按联系人、电话、企业名称等字段进行搜索
导出事件列表
接口：POST /system/incident/export
权限：system:incident:export
支持Excel导出，包含所有字段数据
获取事件详情
接口：GET /system/incident/{id}
权限：system:incident:query
根据ID查询单条记录的详细信息
新增事件
接口：POST /system/incident
权限：system:incident:add
支持防重复提交
必填字段：事件ID(incidentId)
修改事件
接口：PUT /system/incident
权限：system:incident:edit
支持防重复提交
必填字段：ID和事件ID
删除事件
接口：DELETE /system/incident/{ids}
权限：system:incident:remove
支持批量删除，ids用逗号分隔
所有接口都已集成到系统权限体系中，需要相应的权限才能访问。接口支持参数校验、日志记录等特性。

## 创建Sequence Number 机制
现在有这样的需求：需要根据一些表的定义，来得到他们某个字段的INT Squence number。在初始化时候，得到它的当前的最大值后，
缓存到本地，然后就能根据这个最大值得到这些字段的后续的值。
比如这些表的定义 public enum E_TABLE_NAME 
{
	PROJECT= 1
	BUG = 2，

	LAST_SEQUENCE_TABLE，
}
这个E_TABLE_NAME的枚举定义有哪些表需要用这样的机制。
static TCHAR TableName[LAST_SEQUENCE_TABLE][3][42] = {
	_T("[PROJECT]"), _T(""), _T("[PROJECTID]"),
	_T("[Bug]"), _T("[ProjectID]"), _T("[BugID]"),
	}
这个TableName用来定义哪些表的哪个字段是需要用这样的机制。
请用Java来实现
# 
## 这两个table只是为了测试sequence number
CREATE TABLE st_project (
    id                BIGINT       NOT NULL COMMENT 'id',
    project_id       INT          NOT NULL  COMMENT 'projectid',
    project_name VARCHAR(200)               COMMENT 'project nmae',
    PRIMARY KEY (id,project_id)
) ENGINE=InnoDB  COMMENT='项目表';


CREATE TABLE st_bug (
    id                BIGINT       NOT NULL COMMENT 'id',
    project_id       INT          NOT NULL  COMMENT 'projectid',
    bug_id       INT          NOT NULL  COMMENT 'bugd',
    bug_name VARCHAR(200)               COMMENT 'project nmae',
    PRIMARY KEY (id,project_id,bug_id)
) ENGINE=InnoDB  COMMENT='Bug表';